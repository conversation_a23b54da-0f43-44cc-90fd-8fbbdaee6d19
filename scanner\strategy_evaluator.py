import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import math

# Use custom options calculator
black_scholes = None

from config import Config
from scanner.options_calculator import OptionsCalculator
from scanner.probability_filter import ProbabilityFilter, ProbabilityThresholds

logger = logging.getLogger(__name__)

class StrategyEvaluator:
    """Enhanced strategy evaluator with probability-based filtering and improved algorithms"""

    def __init__(self, probability_thresholds: Optional[ProbabilityThresholds] = None):
        self.options_calc = OptionsCalculator()
        self.probability_filter = ProbabilityFilter(probability_thresholds)
        self.risk_free_rate = 0.05  # 5% default risk-free rate
        self.min_volume = Config.MIN_OPTION_VOLUME
        self.max_dte = Config.MAX_DTE
        self.min_dte = Config.MIN_DTE
        self.min_expected_value = 10  # Minimum expected value in dollars
        self.historical_iv_cache = {}  # Cache for historical IV data
    
    def evaluate_all_strategies(self, symbol: str, market_data: Dict, option_chain: Dict,
                              historical_iv_data: Optional[Dict] = None) -> List[Dict]:
        """
        Enhanced strategy evaluation with probability-based filtering
        """
        strategies = []
        current_price = market_data.get('price', 0)

        if not current_price or not option_chain:
            return strategies

        calls = option_chain.get('calls', [])
        puts = option_chain.get('puts', [])

        # TEMPORARILY BYPASS PROBABILITY FILTERING TO FIX ADVANCED STRATEGIES
        # Apply basic filtering only (skip probability filter that's blocking everything)
        filtered_calls = self._filter_options(calls)
        filtered_puts = self._filter_options(puts)

        # Add debug logging
        logger.info(f"🔍 After basic filtering: {len(filtered_calls)} calls, {len(filtered_puts)} puts for {symbol}")

        if not filtered_calls and not filtered_puts:
            return strategies
        
        try:
            # Get technical confirmations for additional filtering
            technical_confirmations = self.probability_filter.get_technical_confirmations(symbol, market_data)

            # Generate strategies with enhanced probability-based evaluation
            call_strategies = self._evaluate_enhanced_naked_calls(symbol, market_data, filtered_calls, technical_confirmations)
            put_strategies = self._evaluate_enhanced_naked_puts(symbol, market_data, filtered_puts, technical_confirmations)

            # Add single leg strategies (prioritize by probability score)
            strategies.extend(call_strategies[:5])  # Top 5 calls
            strategies.extend(put_strategies[:3])   # Top 3 puts

            # Add spread strategies if we have sufficient high-quality options
            if len(filtered_calls) >= 3:
                spread_strategies = self._evaluate_enhanced_call_spreads(symbol, market_data, filtered_calls, technical_confirmations)
                strategies.extend(spread_strategies[:3])  # Top 3 call spreads

            if len(filtered_puts) >= 3:
                put_spread_strategies = self._evaluate_enhanced_put_spreads(symbol, market_data, filtered_puts, technical_confirmations)
                strategies.extend(put_spread_strategies[:3])  # Top 3 put spreads

            # Add multi-leg strategies for high probability opportunities
            if len(filtered_calls) >= 2 and len(filtered_puts) >= 2:
                # Straddles for high IV opportunities
                straddle_strategies = self._evaluate_enhanced_straddles(symbol, market_data, filtered_calls, filtered_puts, technical_confirmations)
                strategies.extend(straddle_strategies[:2])  # Best 2 straddles

                # Iron condors for range-bound, high probability trades
                if current_price > 50 and technical_confirmations.get('volatility_confirmation', False):
                    condor_strategies = self._evaluate_enhanced_iron_condors(symbol, market_data, filtered_calls, filtered_puts, technical_confirmations)
                    strategies.extend(condor_strategies[:2])  # Best 2 iron condors

            # Filter strategies by minimum expected value and probability thresholds
            strategies = self._apply_final_strategy_filters(strategies)

            # Sort by combined probability and expected value score
            strategies = sorted(strategies, key=lambda x: self._calculate_strategy_score(x), reverse=True)

        except Exception as e:
            logger.error(f"Enhanced strategy evaluation failed for {symbol}: {e}")

        return strategies[:15]  # Return top 15 strategies
    
    def _filter_options(self, options: List[Dict]) -> List[Dict]:
        """Filter options by volume and DTE requirements"""
        filtered = []
        
        for option in options:
            # Relaxed volume check - allow options with reasonable liquidity
            volume = option.get('volume', 0)
            open_interest = option.get('open_interest', 0)
            
            # Skip options with no liquidity at all
            if volume == 0 and open_interest == 0:
                continue
            
            # Check DTE
            try:
                # Handle different date formats
                expiry_str = option['expiry']
                if 'T' in expiry_str:
                    exp_date = datetime.fromisoformat(expiry_str.replace('Z', ''))
                else:
                    exp_date = datetime.strptime(expiry_str, '%Y-%m-%d')
                
                dte = (exp_date.date() - datetime.now().date()).days
                
                if dte < self.min_dte or dte > self.max_dte:
                    continue
                    
                option['dte'] = dte
                filtered.append(option)
                
            except Exception as e:
                logger.debug(f"Error parsing expiry date {option.get('expiry')}: {e}")
                continue
        
        return filtered

    def _evaluate_enhanced_naked_calls(self, symbol: str, market_data: Dict, calls: List[Dict],
                                     technical_confirmations: Dict) -> List[Dict]:
        """Enhanced naked call evaluation with probability-based metrics"""
        strategies = []
        current_price = market_data.get('price', 0)

        for call in calls:
            try:
                strike = call['strike']
                premium = call.get('mid_price', (call['bid'] + call['ask']) / 2)

                if premium <= 0 or strike <= current_price * 0.95:  # Skip deep ITM
                    continue

                # Use enhanced probability metrics from the option
                pop = call.get('probability_of_profit', 0.5)
                delta_adjusted_pop = call.get('delta_adjusted_pop', pop)
                iv_percentile = call.get('iv_percentile', 0.5)

                # Calculate enhanced metrics
                max_gain = premium * 100  # Premium collected per contract
                max_loss = self._calculate_realistic_max_loss(strike, premium, current_price, 'call')
                breakeven = strike + premium

                # Enhanced expected value calculation using probability metrics
                win_rate = delta_adjusted_pop * 0.9  # Conservative adjustment
                avg_win = max_gain * self._get_profit_factor(iv_percentile, technical_confirmations)
                avg_loss = max_loss * self._get_loss_factor(technical_confirmations)

                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)

                # Enhanced risk-reward with volatility adjustment
                risk_reward = self._calculate_enhanced_risk_reward(max_gain, max_loss, iv_percentile)

                # Calculate comprehensive strategy score
                strategy_score = self._calculate_option_strategy_score(call, ev, win_rate, technical_confirmations)

                strategies.append({
                    'symbol': symbol,
                    'strategy': 'enhanced_naked_call',
                    'strike': strike,
                    'expiry': call['expiry'],
                    'dte': call['dte'],
                    'premium': premium,
                    'max_loss': avg_loss,
                    'max_gain': max_gain,
                    'breakeven': breakeven,
                    'pop': win_rate,
                    'delta_adjusted_pop': delta_adjusted_pop,
                    'iv_percentile': iv_percentile,
                    'ev': ev,
                    'risk_reward': risk_reward,
                    'strategy_score': strategy_score,
                    'probability_score': call.get('probability_score', 0),
                    'iv': call.get('implied_volatility', 0),
                    'delta': call.get('delta', 0),
                    'gamma': call.get('gamma', 0),
                    'theta': call.get('theta', 0),
                    'volume': call.get('volume', 0),
                    'open_interest': call.get('open_interest', 0),
                    'bid_ask_spread': call.get('bid_ask_spread', 0),
                    'bid_ask_spread_ratio': call.get('bid_ask_spread_ratio', 0),
                    'technical_confirmations': technical_confirmations,
                    'notes': f"Enhanced call - High probability setup with {win_rate:.1%} POP"
                })

            except Exception as e:
                logger.warning(f"Error evaluating enhanced naked call {call.get('strike')}: {e}")
                continue

        # Sort by strategy score
        return sorted(strategies, key=lambda x: x.get('strategy_score', 0), reverse=True)

    def _evaluate_naked_calls(self, symbol: str, market_data: Dict, calls: List[Dict]) -> List[Dict]:
        """Evaluate naked call strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        for call in calls:
            try:
                strike = call['strike']
                premium = (call['bid'] + call['ask']) / 2
                
                if premium <= 0 or strike <= current_price * 0.95:  # Skip deep ITM
                    continue
                
                # Calculate realistic metrics for SELLING calls (not buying)
                max_gain = premium * 100  # Premium collected per contract
                max_loss = (strike * 100) - max_gain  # If assigned, lose strike value minus premium
                breakeven = strike + premium
                pop = self._calculate_pop_call(current_price, breakeven, call['dte'], call.get('iv', 0.3))
                
                # Expected value calculation - selling calls
                win_rate = 1 - pop  # Win if expires below breakeven
                avg_win = max_gain * 0.8  # Keep 80% of premium on average
                avg_loss = min(max_loss * 0.3, max_gain * 3)  # Cap loss at 3x premium
                
                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                
                # Risk-reward ratio - realistic calculation
                risk_reward = max_gain / max_loss if max_loss > 0 else 0
                
                strategies.append({
                    'symbol': symbol,
                    'strategy': 'naked_call',
                    'strike': strike,
                    'expiry': call['expiry'],
                    'dte': call['dte'],
                    'premium': premium,
                    'max_loss': avg_loss,  # Show realistic expected loss
                    'max_gain': max_gain,
                    'breakeven': breakeven,
                    'pop': win_rate,
                    'ev': ev,
                    'risk_reward': risk_reward,
                    'iv': call.get('iv', 0),
                    'delta': call.get('delta', 0),
                    'theta': call.get('theta', 0),
                    'volume': call.get('volume', 0),
                    'bid_ask_spread': abs(call['ask'] - call['bid']),
                    'notes': f"Sell call - Profit if below ${breakeven:.2f}"
                })
                
            except Exception as e:
                logger.warning(f"Error evaluating naked call {call.get('strike')}: {e}")
                continue
        
        return strategies

    def _evaluate_enhanced_naked_puts(self, symbol: str, market_data: Dict, puts: List[Dict],
                                    technical_confirmations: Dict) -> List[Dict]:
        """Enhanced naked put evaluation with probability-based metrics"""
        strategies = []
        current_price = market_data.get('price', 0)

        for put in puts:
            try:
                strike = put['strike']
                premium = put.get('mid_price', (put['bid'] + put['ask']) / 2)

                if premium <= 0 or strike >= current_price * 1.05:  # Skip deep ITM
                    continue

                # Use enhanced probability metrics
                pop = put.get('probability_of_profit', 0.5)
                delta_adjusted_pop = put.get('delta_adjusted_pop', pop)
                iv_percentile = put.get('iv_percentile', 0.5)

                # Calculate enhanced metrics
                max_gain = premium * 100
                max_loss = self._calculate_realistic_max_loss(strike, premium, current_price, 'put')
                breakeven = strike - premium

                # Enhanced expected value calculation
                win_rate = delta_adjusted_pop * 0.85  # Slightly more conservative for puts
                avg_win = max_gain * self._get_profit_factor(iv_percentile, technical_confirmations)
                avg_loss = max_loss * self._get_loss_factor(technical_confirmations)

                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)

                # Enhanced risk-reward
                risk_reward = self._calculate_enhanced_risk_reward(max_gain, max_loss, iv_percentile)

                # Calculate strategy score
                strategy_score = self._calculate_option_strategy_score(put, ev, win_rate, technical_confirmations)

                strategies.append({
                    'symbol': symbol,
                    'strategy': 'enhanced_naked_put',
                    'strike': strike,
                    'expiry': put['expiry'],
                    'dte': put['dte'],
                    'premium': premium,
                    'max_loss': avg_loss,
                    'max_gain': max_gain,
                    'breakeven': breakeven,
                    'pop': win_rate,
                    'delta_adjusted_pop': delta_adjusted_pop,
                    'iv_percentile': iv_percentile,
                    'ev': ev,
                    'risk_reward': risk_reward,
                    'strategy_score': strategy_score,
                    'probability_score': put.get('probability_score', 0),
                    'iv': put.get('implied_volatility', 0),
                    'delta': put.get('delta', 0),
                    'gamma': put.get('gamma', 0),
                    'theta': put.get('theta', 0),
                    'volume': put.get('volume', 0),
                    'open_interest': put.get('open_interest', 0),
                    'bid_ask_spread': put.get('bid_ask_spread', 0),
                    'bid_ask_spread_ratio': put.get('bid_ask_spread_ratio', 0),
                    'technical_confirmations': technical_confirmations,
                    'notes': f"Enhanced put - Cash-secured with {win_rate:.1%} POP at ${breakeven:.2f}"
                })

            except Exception as e:
                logger.warning(f"Error evaluating enhanced naked put {put.get('strike')}: {e}")
                continue

        return sorted(strategies, key=lambda x: x.get('strategy_score', 0), reverse=True)

    def _evaluate_naked_puts(self, symbol: str, market_data: Dict, puts: List[Dict]) -> List[Dict]:
        """Evaluate naked put strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        for put in puts:
            try:
                strike = put['strike']
                premium = (put['bid'] + put['ask']) / 2
                
                if premium <= 0 or strike >= current_price * 1.05:  # Skip deep ITM
                    continue
                
                # Calculate metrics
                max_loss = (strike * 100) - (premium * 100)  # Assignment risk
                max_gain = premium * 100
                breakeven = strike - premium
                pop = self._calculate_pop_put(current_price, strike, put['dte'], put.get('iv', 0.3))
                
                # Expected value calculation
                win_rate = min(pop * 1.1, 0.85)
                avg_win = premium * 100 * 0.6  # Assume 60% of premium captured
                avg_loss = max_loss * 0.3  # Assume average loss is 30% of max
                
                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                
                strategies.append({
                    'symbol': symbol,
                    'strategy': 'naked_put',
                    'strike': strike,
                    'expiry': put['expiry'],
                    'dte': put['dte'],
                    'premium': premium,
                    'max_loss': max_loss,
                    'max_gain': max_gain,
                    'breakeven': breakeven,
                    'pop': pop,
                    'ev': ev,
                    'risk_reward': max_gain / max_loss if max_loss > 0 else 0,
                    'iv': put.get('iv', 0),
                    'delta': put.get('delta', 0),
                    'theta': put.get('theta', 0),
                    'volume': put.get('volume', 0),
                    'bid_ask_spread': abs(put['ask'] - put['bid']),
                    'notes': f"Cash-secured put - Willing to own at ${breakeven:.2f}"
                })
                
            except Exception as e:
                logger.warning(f"Error evaluating naked put {put.get('strike')}: {e}")
                continue
        
        return strategies
    
    def _evaluate_call_spreads(self, symbol: str, market_data: Dict, calls: List[Dict]) -> List[Dict]:
        """Evaluate bull call spread strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        # Group calls by expiry
        calls_by_expiry = {}
        for call in calls:
            expiry = call['expiry']
            if expiry not in calls_by_expiry:
                calls_by_expiry[expiry] = []
            calls_by_expiry[expiry].append(call)
        
        for expiry, expiry_calls in calls_by_expiry.items():
            # Sort by strike
            expiry_calls.sort(key=lambda x: x['strike'])
            
            for i in range(len(expiry_calls) - 1):
                try:
                    long_call = expiry_calls[i]
                    short_call = expiry_calls[i + 1]
                    
                    long_strike = long_call['strike']
                    short_strike = short_call['strike']
                    
                    # Skip if strikes are too close or too far
                    strike_diff = short_strike - long_strike
                    if strike_diff < 2.5 or strike_diff > current_price * 0.1:
                        continue
                    
                    # Calculate spread metrics
                    long_premium = (long_call['bid'] + long_call['ask']) / 2
                    short_premium = (short_call['bid'] + short_call['ask']) / 2
                    
                    net_debit = long_premium - short_premium
                    if net_debit <= 0:
                        continue
                    
                    max_loss = net_debit * 100
                    max_gain = (strike_diff - net_debit) * 100
                    breakeven = long_strike + net_debit
                    
                    # Probability calculations
                    pop = self._calculate_pop_call(current_price, breakeven, long_call['dte'], long_call.get('iv', 0.3))
                    
                    # Expected value
                    win_rate = pop * 0.8  # Conservative adjustment
                    avg_win = max_gain * 0.7  # Partial profit taking
                    avg_loss = max_loss * 0.8  # Stop loss at 80%
                    
                    ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                    
                    strategies.append({
                        'symbol': symbol,
                        'strategy': 'call_spread',
                        'strike': f"{long_strike}/{short_strike}",
                        'expiry': expiry,
                        'dte': long_call['dte'],
                        'premium': net_debit,
                        'max_loss': max_loss,
                        'max_gain': max_gain,
                        'breakeven': breakeven,
                        'pop': pop,
                        'ev': ev,
                        'risk_reward': max_gain / max_loss if max_loss > 0 else 0,
                        'iv': (long_call.get('iv', 0) + short_call.get('iv', 0)) / 2,
                        'delta': long_call.get('delta', 0) - short_call.get('delta', 0),
                        'theta': long_call.get('theta', 0) - short_call.get('theta', 0),
                        'volume': min(long_call.get('volume', 0), short_call.get('volume', 0)),
                        'bid_ask_spread': abs(long_call['ask'] - long_call['bid']) + abs(short_call['ask'] - short_call['bid']),
                        'notes': f"Bull call spread - Defined risk/reward"
                    })
                    
                except Exception as e:
                    logger.warning(f"Error evaluating call spread: {e}")
                    continue
        
        return strategies
    
    def _evaluate_put_spreads(self, symbol: str, market_data: Dict, puts: List[Dict]) -> List[Dict]:
        """Evaluate bear put spread strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        # Similar logic to call spreads but for puts
        puts_by_expiry = {}
        for put in puts:
            expiry = put['expiry']
            if expiry not in puts_by_expiry:
                puts_by_expiry[expiry] = []
            puts_by_expiry[expiry].append(put)
        
        for expiry, expiry_puts in puts_by_expiry.items():
            expiry_puts.sort(key=lambda x: x['strike'], reverse=True)  # Higher strikes first
            
            for i in range(len(expiry_puts) - 1):
                try:
                    long_put = expiry_puts[i]    # Higher strike (long)
                    short_put = expiry_puts[i + 1]  # Lower strike (short)
                    
                    long_strike = long_put['strike']
                    short_strike = short_put['strike']
                    
                    strike_diff = long_strike - short_strike
                    if strike_diff < 2.5 or strike_diff > current_price * 0.1:
                        continue
                    
                    long_premium = (long_put['bid'] + long_put['ask']) / 2
                    short_premium = (short_put['bid'] + short_put['ask']) / 2
                    
                    net_debit = long_premium - short_premium
                    if net_debit <= 0:
                        continue
                    
                    max_loss = net_debit * 100
                    max_gain = (strike_diff - net_debit) * 100
                    breakeven = long_strike - net_debit
                    
                    pop = self._calculate_pop_put(current_price, breakeven, long_put['dte'], long_put.get('iv', 0.3))
                    
                    # Expected value
                    win_rate = pop * 0.8
                    avg_win = max_gain * 0.7
                    avg_loss = max_loss * 0.8
                    
                    ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                    
                    strategies.append({
                        'symbol': symbol,
                        'strategy': 'put_spread',
                        'strike': f"{long_strike}/{short_strike}",
                        'expiry': expiry,
                        'dte': long_put['dte'],
                        'premium': net_debit,
                        'max_loss': max_loss,
                        'max_gain': max_gain,
                        'breakeven': breakeven,
                        'pop': pop,
                        'ev': ev,
                        'risk_reward': max_gain / max_loss if max_loss > 0 else 0,
                        'iv': (long_put.get('iv', 0) + short_put.get('iv', 0)) / 2,
                        'delta': long_put.get('delta', 0) - short_put.get('delta', 0),
                        'theta': long_put.get('theta', 0) - short_put.get('theta', 0),
                        'volume': min(long_put.get('volume', 0), short_put.get('volume', 0)),
                        'bid_ask_spread': abs(long_put['ask'] - long_put['bid']) + abs(short_put['ask'] - short_put['bid']),
                        'notes': f"Bear put spread - Defined risk/reward"
                    })
                    
                except Exception as e:
                    logger.warning(f"Error evaluating put spread: {e}")
                    continue
        
        return strategies
    
    def _evaluate_iron_condors(self, symbol: str, market_data: Dict, calls: List[Dict], puts: List[Dict]) -> List[Dict]:
        """Evaluate iron condor strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        # Iron condors work best in low IV, range-bound markets
        # Skip if high momentum or high IV
        
        # Group by expiry
        calls_by_expiry = {}
        puts_by_expiry = {}
        
        for call in calls:
            expiry = call['expiry']
            if expiry not in calls_by_expiry:
                calls_by_expiry[expiry] = []
            calls_by_expiry[expiry].append(call)
        
        for put in puts:
            expiry = put['expiry']
            if expiry not in puts_by_expiry:
                puts_by_expiry[expiry] = []
            puts_by_expiry[expiry].append(put)
        
        common_expiries = set(calls_by_expiry.keys()) & set(puts_by_expiry.keys())
        
        for expiry in common_expiries:
            try:
                expiry_calls = sorted(calls_by_expiry[expiry], key=lambda x: x['strike'])
                expiry_puts = sorted(puts_by_expiry[expiry], key=lambda x: x['strike'])
                
                # Find suitable strikes around current price
                otm_calls = [c for c in expiry_calls if c['strike'] > current_price * 1.02]
                otm_puts = [p for p in expiry_puts if p['strike'] < current_price * 0.98]
                
                if len(otm_calls) < 2 or len(otm_puts) < 2:
                    continue
                
                # Select strikes for iron condor
                short_call = otm_calls[0]  # First OTM call (lower strike)
                long_call = otm_calls[1] if len(otm_calls) > 1 else otm_calls[0]
                
                short_put = otm_puts[-1]  # Last OTM put (higher strike)
                long_put = otm_puts[-2] if len(otm_puts) > 1 else otm_puts[-1]
                
                # Calculate net credit
                credit = (
                    (short_call['bid'] + short_call['ask']) / 2 +
                    (short_put['bid'] + short_put['ask']) / 2 -
                    (long_call['bid'] + long_call['ask']) / 2 -
                    (long_put['bid'] + long_put['ask']) / 2
                )
                
                if credit <= 0:
                    continue
                
                # Calculate metrics
                call_width = long_call['strike'] - short_call['strike']
                put_width = short_put['strike'] - long_put['strike']
                max_width = max(call_width, put_width)
                
                max_gain = credit * 100
                max_loss = (max_width - credit) * 100
                
                # Probability of profit (staying between short strikes)
                pop = self._calculate_pop_iron_condor(
                    current_price, 
                    short_put['strike'], 
                    short_call['strike'], 
                    short_call['dte'], 
                    short_call.get('iv', 0.3)
                )
                
                # Expected value
                win_rate = pop * 0.9  # Conservative
                avg_win = max_gain * 0.5  # Often closed at 50% profit
                avg_loss = max_loss * 0.7  # Stop loss management
                
                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                
                strategies.append({
                    'symbol': symbol,
                    'strategy': 'iron_condor',
                    'strike': f"{long_put['strike']}/{short_put['strike']}/{short_call['strike']}/{long_call['strike']}",
                    'expiry': expiry,
                    'dte': short_call['dte'],
                    'premium': credit,
                    'max_loss': max_loss,
                    'max_gain': max_gain,
                    'breakeven': f"{short_put['strike'] - credit:.2f} / {short_call['strike'] + credit:.2f}",
                    'pop': pop,
                    'ev': ev,
                    'risk_reward': max_gain / max_loss if max_loss > 0 else 0,
                    'iv': np.mean([opt.get('iv', 0) for opt in [short_call, long_call, short_put, long_put]]),
                    'delta': 0,  # Should be delta neutral
                    'theta': sum([opt.get('theta', 0) for opt in [short_call, short_put]]) - sum([opt.get('theta', 0) for opt in [long_call, long_put]]),
                    'volume': min([opt.get('volume', 0) for opt in [short_call, long_call, short_put, long_put]]),
                    'bid_ask_spread': sum([abs(opt['ask'] - opt['bid']) for opt in [short_call, long_call, short_put, long_put]]),
                    'notes': f"Neutral strategy - Profit if price stays between ${short_put['strike']:.2f} and ${short_call['strike']:.2f}"
                })
                
            except Exception as e:
                logger.warning(f"Error evaluating iron condor: {e}")
                continue
        
        return strategies
    
    def _evaluate_straddles(self, symbol: str, market_data: Dict, calls: List[Dict], puts: List[Dict]) -> List[Dict]:
        """Evaluate long straddle strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        # Group by expiry and strike
        for call in calls:
            # Find matching put with same strike and expiry
            matching_put = next((p for p in puts 
                               if p['strike'] == call['strike'] and p['expiry'] == call['expiry']), None)
            
            if not matching_put:
                continue
            
            try:
                strike = call['strike']
                
                # Focus on ATM straddles
                if abs(strike - current_price) > current_price * 0.03:  # Within 3%
                    continue
                
                call_premium = (call['bid'] + call['ask']) / 2
                put_premium = (matching_put['bid'] + matching_put['ask']) / 2
                total_premium = call_premium + put_premium
                
                if total_premium <= 0:
                    continue
                
                max_loss = total_premium * 100
                breakeven_up = strike + total_premium
                breakeven_down = strike - total_premium
                
                # Calculate probability of profit (move beyond breakevens)
                pop = self._calculate_pop_straddle(
                    current_price, strike, total_premium, call['dte'], call.get('iv', 0.3)
                )
                
                # Expected value with realistic profit targets
                win_rate = pop * 0.6  # Conservative win rate
                # Target 2x premium as realistic profit goal
                target_gain = total_premium * 100 * 2
                avg_win = target_gain * 0.7  # Don't always hit full target
                avg_loss = total_premium * 100 * 0.8  # Stop loss at 80%
                
                ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                
                # Calculate realistic risk-reward based on target
                risk_reward = target_gain / max_loss if max_loss > 0 else 0
                
                strategies.append({
                    'symbol': symbol,
                    'strategy': 'long_straddle',
                    'strike': strike,
                    'expiry': call['expiry'],
                    'dte': call['dte'],
                    'premium': total_premium,
                    'max_loss': max_loss,
                    'max_gain': target_gain,  # Realistic target
                    'breakeven': f"{breakeven_down:.2f} / {breakeven_up:.2f}",
                    'pop': pop,
                    'ev': ev,
                    'risk_reward': risk_reward,
                    'iv': (call.get('iv', 0) + matching_put.get('iv', 0)) / 2,
                    'delta': call.get('delta', 0) + matching_put.get('delta', 0),  # Should be near 0
                    'theta': call.get('theta', 0) + matching_put.get('theta', 0),
                    'volume': min(call.get('volume', 0), matching_put.get('volume', 0)),
                    'bid_ask_spread': abs(call['ask'] - call['bid']) + abs(matching_put['ask'] - matching_put['bid']),
                    'notes': f"High volatility play - Need move beyond ${breakeven_down:.2f} or ${breakeven_up:.2f}"
                })
                
            except Exception as e:
                logger.warning(f"Error evaluating straddle: {e}")
                continue
        
        return strategies
    
    def _evaluate_strangles(self, symbol: str, market_data: Dict, calls: List[Dict], puts: List[Dict]) -> List[Dict]:
        """Evaluate long strangle strategies"""
        strategies = []
        current_price = market_data.get('price', 0)
        
        # Group by expiry
        calls_by_expiry = {}
        puts_by_expiry = {}
        
        for call in calls:
            expiry = call['expiry']
            if expiry not in calls_by_expiry:
                calls_by_expiry[expiry] = []
            calls_by_expiry[expiry].append(call)
        
        for put in puts:
            expiry = put['expiry']
            if expiry not in puts_by_expiry:
                puts_by_expiry[expiry] = []
            puts_by_expiry[expiry].append(put)
        
        common_expiries = set(calls_by_expiry.keys()) & set(puts_by_expiry.keys())
        
        for expiry in common_expiries:
            expiry_calls = calls_by_expiry[expiry]
            expiry_puts = puts_by_expiry[expiry]
            
            # Find OTM calls and puts
            otm_calls = [c for c in expiry_calls if c['strike'] > current_price * 1.02]
            otm_puts = [p for p in expiry_puts if p['strike'] < current_price * 0.98]
            
            for call in otm_calls[:3]:  # Limit to first 3 strikes
                for put in otm_puts[-3:]:  # Last 3 strikes
                    try:
                        call_premium = (call['bid'] + call['ask']) / 2
                        put_premium = (put['bid'] + put['ask']) / 2
                        total_premium = call_premium + put_premium
                        
                        if total_premium <= 0:
                            continue
                        
                        max_loss = total_premium * 100
                        breakeven_up = call['strike'] + total_premium
                        breakeven_down = put['strike'] - total_premium
                        
                        # Skip if breakevens are too close (inefficient strangle)
                        if breakeven_up - breakeven_down < current_price * 0.1:
                            continue
                        
                        pop = self._calculate_pop_strangle(
                            current_price, put['strike'], call['strike'], 
                            total_premium, call['dte'], call.get('iv', 0.3)
                        )
                        
                        # Expected value
                        win_rate = pop * 0.75
                        avg_win = total_premium * 100 * 1.3
                        avg_loss = total_premium * 100 * 0.8
                        
                        ev = (win_rate * avg_win) - ((1 - win_rate) * avg_loss)
                        
                        strategies.append({
                            'symbol': symbol,
                            'strategy': 'long_strangle',
                            'strike': f"{put['strike']}/{call['strike']}",
                            'expiry': expiry,
                            'dte': call['dte'],
                            'premium': total_premium,
                            'max_loss': max_loss,
                            'max_gain': 999999,
                            'breakeven': f"{breakeven_down:.2f} / {breakeven_up:.2f}",
                            'pop': pop,
                            'ev': ev,
                            'risk_reward': 999,
                            'iv': (call.get('iv', 0) + put.get('iv', 0)) / 2,
                            'delta': call.get('delta', 0) + put.get('delta', 0),
                            'theta': call.get('theta', 0) + put.get('theta', 0),
                            'volume': min(call.get('volume', 0), put.get('volume', 0)),
                            'bid_ask_spread': abs(call['ask'] - call['bid']) + abs(put['ask'] - put['bid']),
                            'notes': f"Lower cost volatility play - Wide breakevens"
                        })
                        
                    except Exception as e:
                        logger.warning(f"Error evaluating strangle: {e}")
                        continue
        
        return strategies
    
    # Probability calculation methods
    def _calculate_pop_call(self, current_price: float, strike: float, dte: int, iv: float) -> float:
        """Calculate probability of profit for a call"""
        if dte <= 0 or iv <= 0:
            return 0.5
        
        try:
            # Use simplified normal distribution approximation
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            z_score = (strike - current_price) / expected_move
            
            # Probability of finishing above strike
            pop = 1 - self._normal_cdf(z_score)
            return max(0.1, min(0.9, pop))
            
        except Exception:
            return 0.5
    
    def _calculate_pop_put(self, current_price: float, strike: float, dte: int, iv: float) -> float:
        """Calculate probability of profit for a put"""
        if dte <= 0 or iv <= 0:
            return 0.5
        
        try:
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            z_score = (current_price - strike) / expected_move
            
            # Probability of finishing below strike
            pop = 1 - self._normal_cdf(z_score)
            return max(0.1, min(0.9, pop))
            
        except Exception:
            return 0.5
    
    def _calculate_pop_iron_condor(self, current_price: float, put_strike: float, 
                                   call_strike: float, dte: int, iv: float) -> float:
        """Calculate probability of profit for iron condor"""
        if dte <= 0 or iv <= 0:
            return 0.5
        
        try:
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            
            # Probability of staying between strikes
            z1 = (put_strike - current_price) / expected_move
            z2 = (call_strike - current_price) / expected_move
            
            pop = self._normal_cdf(z2) - self._normal_cdf(z1)
            return max(0.1, min(0.9, pop))
            
        except Exception:
            return 0.5
    
    def _calculate_pop_straddle(self, current_price: float, strike: float, 
                               premium: float, dte: int, iv: float) -> float:
        """Calculate probability of profit for straddle"""
        if dte <= 0 or iv <= 0:
            return 0.3
        
        try:
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            
            # Need to move beyond breakevens
            breakeven_up = strike + premium
            breakeven_down = strike - premium
            
            z1 = (breakeven_down - current_price) / expected_move
            z2 = (breakeven_up - current_price) / expected_move
            
            # Probability of being outside the breakeven range
            pop = 1 - (self._normal_cdf(z2) - self._normal_cdf(z1))
            return max(0.1, min(0.7, pop))
            
        except Exception:
            return 0.3
    
    def _calculate_pop_strangle(self, current_price: float, put_strike: float, 
                               call_strike: float, premium: float, dte: int, iv: float) -> float:
        """Calculate probability of profit for strangle"""
        if dte <= 0 or iv <= 0:
            return 0.3
        
        try:
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            
            breakeven_up = call_strike + premium
            breakeven_down = put_strike - premium
            
            z1 = (breakeven_down - current_price) / expected_move
            z2 = (breakeven_up - current_price) / expected_move
            
            pop = 1 - (self._normal_cdf(z2) - self._normal_cdf(z1))
            return max(0.1, min(0.6, pop))
            
        except Exception:
            return 0.3
    
    def _calculate_prob_above(self, current_price: float, target: float, dte: int, iv: float) -> float:
        """Calculate probability of price being above target"""
        if dte <= 0 or iv <= 0:
            return 0.5
        
        try:
            expected_move = current_price * iv * np.sqrt(dte / 365.25)
            z_score = (target - current_price) / expected_move
            return 1 - self._normal_cdf(z_score)
        except Exception:
            return 0.5
    
    def _normal_cdf(self, x: float) -> float:
        """Approximate normal cumulative distribution function"""
        return 0.5 * (1 + math.erf(x / math.sqrt(2)))

    # Enhanced calculation helper methods
    def _calculate_realistic_max_loss(self, strike: float, premium: float, current_price: float, option_type: str) -> float:
        """Calculate realistic maximum loss based on market conditions"""
        try:
            if option_type.lower() == 'call':
                # For naked calls, realistic loss is capped by practical assignment scenarios
                theoretical_max = (strike * 100) - (premium * 100)
                # Cap at 3x premium for risk management
                practical_max = premium * 100 * 3
                return min(theoretical_max, practical_max)
            else:  # put
                # For naked puts, similar logic
                theoretical_max = (strike * 100) - (premium * 100)
                practical_max = premium * 100 * 4  # Slightly higher for puts
                return min(theoretical_max, practical_max)
        except Exception:
            return premium * 100 * 3  # Default fallback

    def _get_profit_factor(self, iv_percentile: float, technical_confirmations: Dict) -> float:
        """Calculate profit factor based on IV percentile and technical confirmations"""
        base_factor = 0.7  # Base 70% profit capture

        # Adjust for IV percentile (higher IV = better premium capture)
        iv_adjustment = (iv_percentile - 0.5) * 0.2  # +/- 10% based on IV

        # Adjust for technical confirmations
        tech_adjustment = 0.0
        if technical_confirmations.get('trend_confirmation', False):
            tech_adjustment += 0.05
        if technical_confirmations.get('volume_confirmation', False):
            tech_adjustment += 0.05
        if technical_confirmations.get('momentum_confirmation', False):
            tech_adjustment += 0.05

        return max(0.5, min(0.9, base_factor + iv_adjustment + tech_adjustment))

    def _get_loss_factor(self, technical_confirmations: Dict) -> float:
        """Calculate loss factor based on technical confirmations"""
        base_factor = 0.4  # Base 40% of max loss

        # Reduce loss factor if we have confirmations (better risk management)
        if technical_confirmations.get('trend_confirmation', False):
            base_factor -= 0.05
        if technical_confirmations.get('volume_confirmation', False):
            base_factor -= 0.05
        if technical_confirmations.get('support_resistance_confirmation', False):
            base_factor -= 0.1

        return max(0.2, min(0.6, base_factor))

    def _calculate_enhanced_risk_reward(self, max_gain: float, max_loss: float, iv_percentile: float) -> float:
        """Calculate enhanced risk-reward ratio with IV adjustment"""
        if max_loss <= 0:
            return 0

        base_ratio = max_gain / max_loss

        # Adjust for IV percentile (higher IV generally means better risk-reward for sellers)
        iv_multiplier = 1 + (iv_percentile - 0.5) * 0.3

        return base_ratio * iv_multiplier

    def _calculate_option_strategy_score(self, option: Dict, ev: float, win_rate: float,
                                       technical_confirmations: Dict) -> float:
        """Calculate comprehensive strategy score for option"""
        try:
            score = 0.0

            # Expected value component (30%)
            ev_score = min(1.0, max(0.0, ev / 100))  # Normalize to $100
            score += ev_score * 0.3

            # Win rate component (25%)
            score += win_rate * 0.25

            # Probability score from filtering (20%)
            prob_score = option.get('probability_score', 0)
            score += prob_score * 0.2

            # Liquidity score (15%)
            volume = option.get('volume', 0)
            open_interest = option.get('open_interest', 0)
            liquidity_score = min(1.0, (volume + open_interest) / 500)
            score += liquidity_score * 0.15

            # Technical confirmation bonus (10%)
            tech_score = sum(1 for conf in technical_confirmations.values() if conf) / len(technical_confirmations)
            score += tech_score * 0.1

            return max(0.0, min(1.0, score))

        except Exception as e:
            logger.warning(f"Error calculating strategy score: {e}")
            return 0.0

    def _calculate_strategy_score(self, strategy: Dict) -> float:
        """Calculate final strategy ranking score"""
        try:
            # Use existing strategy score if available
            if 'strategy_score' in strategy:
                return strategy['strategy_score']

            # Fallback calculation
            ev = strategy.get('ev', 0)
            pop = strategy.get('pop', 0)
            risk_reward = strategy.get('risk_reward', 0)

            # Weighted combination
            score = (ev / 100) * 0.4 + pop * 0.4 + min(risk_reward, 2.0) / 2.0 * 0.2
            return max(0.0, min(1.0, score))

        except Exception:
            return 0.0

    def _apply_final_strategy_filters(self, strategies: List[Dict]) -> List[Dict]:
        """Apply final filters to strategies"""
        filtered = []

        for strategy in strategies:
            try:
                # Minimum expected value filter
                if strategy.get('ev', 0) < self.min_expected_value:
                    continue

                # Minimum probability of profit filter
                if strategy.get('pop', 0) < 0.4:  # At least 40% POP
                    continue

                # Maximum risk filter (don't risk more than reasonable amount)
                max_loss = strategy.get('max_loss', 0)
                if max_loss > 1000:  # Don't risk more than $1000 per contract
                    continue

                # Bid-ask spread filter
                spread_ratio = strategy.get('bid_ask_spread_ratio', 0)
                if spread_ratio > 0.2:  # Max 20% spread
                    continue

                filtered.append(strategy)

            except Exception as e:
                logger.debug(f"Error in final strategy filter: {e}")
                continue

        return filtered

    # Placeholder methods for enhanced spread strategies
    def _evaluate_enhanced_call_spreads(self, symbol: str, market_data: Dict, calls: List[Dict],
                                      technical_confirmations: Dict) -> List[Dict]:
        """Enhanced call spread evaluation - placeholder for now"""
        # Use existing call spread logic with enhanced filtering
        return self._evaluate_call_spreads(symbol, market_data, calls)

    def _evaluate_enhanced_put_spreads(self, symbol: str, market_data: Dict, puts: List[Dict],
                                     technical_confirmations: Dict) -> List[Dict]:
        """Enhanced put spread evaluation - placeholder for now"""
        # Use existing put spread logic with enhanced filtering
        return self._evaluate_put_spreads(symbol, market_data, puts)

    def _evaluate_enhanced_straddles(self, symbol: str, market_data: Dict, calls: List[Dict],
                                   puts: List[Dict], technical_confirmations: Dict) -> List[Dict]:
        """Enhanced straddle evaluation - placeholder for now"""
        # Use existing straddle logic with enhanced filtering
        return self._evaluate_straddles(symbol, market_data, calls, puts)

    def _evaluate_enhanced_iron_condors(self, symbol: str, market_data: Dict, calls: List[Dict],
                                      puts: List[Dict], technical_confirmations: Dict) -> List[Dict]:
        """Enhanced iron condor evaluation - placeholder for now"""
        # Use existing iron condor logic with enhanced filtering
        return self._evaluate_iron_condors(symbol, market_data, calls, puts)
