"""
Alpaca Multi-Leg Options Trading Implementation
Implements Level 3 Options Trading with multi-leg strategies
"""

import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import alpaca_trade_api as tradeapi
from config import Config
import math

logger = logging.getLogger(__name__)

@dataclass
class OptionLeg:
    """Represents a single leg in a multi-leg options strategy"""
    symbol: str
    side: str  # 'buy' or 'sell'
    ratio_qty: str
    position_intent: str  # 'buy_to_open', 'sell_to_open', 'buy_to_close', 'sell_to_close'

@dataclass
class MultiLegOrder:
    """Represents a complete multi-leg options order"""
    order_class: str = "mleg"
    qty: str = "1"
    type: str = "limit"
    limit_price: str = "0.00"
    time_in_force: str = "day"
    legs: List[OptionLeg] = None
    
    def __post_init__(self):
        if self.legs is None:
            self.legs = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API submission"""
        return {
            "order_class": self.order_class,
            "qty": self.qty,
            "type": self.type,
            "limit_price": self.limit_price,
            "time_in_force": self.time_in_force,
            "legs": [asdict(leg) for leg in self.legs]
        }

class AlpacaMultiLegTrader:
    """
    Implements Alpaca's Level 3 Options Trading with multi-leg strategies
    """
    
    def __init__(self):
        """Initialize the multi-leg trader"""
        try:
            self.api = tradeapi.REST(
                Config.ALPACA_API_KEY,
                Config.ALPACA_SECRET_KEY,
                Config.ALPACA_BASE_URL,
                api_version='v2'
            )
            logger.info("✅ Alpaca Multi-Leg Trader initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Alpaca Multi-Leg Trader: {e}")
            self.api = None
    
    def _gcd(self, a: int, b: int) -> int:
        """Calculate Greatest Common Divisor"""
        while b:
            a, b = b, a % b
        return a
    
    def _simplify_ratios(self, ratios: List[int]) -> List[int]:
        """Simplify ratios to their lowest terms (GCD requirement)"""
        if not ratios:
            return ratios
        
        # Find GCD of all ratios
        gcd = ratios[0]
        for ratio in ratios[1:]:
            gcd = self._gcd(gcd, ratio)
        
        # Divide all ratios by GCD
        return [ratio // gcd for ratio in ratios]
    
    def create_long_call_spread(self, symbol: str, lower_strike: float, higher_strike: float, 
                               expiration: str, limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Create a Long Call Spread (Bull Call Spread)
        Buy lower strike call, sell higher strike call
        """
        try:
            # Format option symbols
            lower_call_symbol = self._format_option_symbol(symbol, expiration, 'C', lower_strike)
            higher_call_symbol = self._format_option_symbol(symbol, expiration, 'C', higher_strike)
            
            legs = [
                OptionLeg(
                    symbol=lower_call_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                ),
                OptionLeg(
                    symbol=higher_call_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                )
            ]
            
            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )
            
            logger.info(f"✅ Created Long Call Spread: {lower_strike}/{higher_strike}")
            return order
            
        except Exception as e:
            logger.error(f"❌ Failed to create Long Call Spread: {e}")
            return None
    
    def create_long_put_spread(self, symbol: str, higher_strike: float, lower_strike: float,
                              expiration: str, limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Create a Long Put Spread (Bear Put Spread)
        Buy higher strike put, sell lower strike put
        """
        try:
            # Format option symbols
            higher_put_symbol = self._format_option_symbol(symbol, expiration, 'P', higher_strike)
            lower_put_symbol = self._format_option_symbol(symbol, expiration, 'P', lower_strike)
            
            legs = [
                OptionLeg(
                    symbol=higher_put_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                ),
                OptionLeg(
                    symbol=lower_put_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                )
            ]
            
            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )
            
            logger.info(f"✅ Created Long Put Spread: {higher_strike}/{lower_strike}")
            return order
            
        except Exception as e:
            logger.error(f"❌ Failed to create Long Put Spread: {e}")
            return None
    
    def create_iron_condor(self, symbol: str, put_lower_strike: float, put_higher_strike: float,
                          call_lower_strike: float, call_higher_strike: float, expiration: str,
                          limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Create an Iron Condor
        Sell put spread + sell call spread
        """
        try:
            # Format option symbols
            put_lower_symbol = self._format_option_symbol(symbol, expiration, 'P', put_lower_strike)
            put_higher_symbol = self._format_option_symbol(symbol, expiration, 'P', put_higher_strike)
            call_lower_symbol = self._format_option_symbol(symbol, expiration, 'C', call_lower_strike)
            call_higher_symbol = self._format_option_symbol(symbol, expiration, 'C', call_higher_strike)
            
            legs = [
                # Put spread (sell higher, buy lower)
                OptionLeg(
                    symbol=put_lower_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                ),
                OptionLeg(
                    symbol=put_higher_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                # Call spread (sell lower, buy higher)
                OptionLeg(
                    symbol=call_lower_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                OptionLeg(
                    symbol=call_higher_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                )
            ]
            
            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )
            
            logger.info(f"✅ Created Iron Condor: P{put_lower_strike}/{put_higher_strike} C{call_lower_strike}/{call_higher_strike}")
            return order
            
        except Exception as e:
            logger.error(f"❌ Failed to create Iron Condor: {e}")
            return None
    
    def _format_option_symbol(self, underlying: str, expiration: str, option_type: str, strike: float) -> str:
        """
        Format option symbol according to OCC standard
        Example: AAPL250117C00200000
        """
        try:
            # Parse expiration date
            exp_date = datetime.strptime(expiration, '%Y-%m-%d')
            exp_str = exp_date.strftime('%y%m%d')
            
            # Format strike price (multiply by 1000 and pad to 8 digits)
            strike_str = f"{int(strike * 1000):08d}"
            
            symbol = f"{underlying}{exp_str}{option_type}{strike_str}"
            return symbol
            
        except Exception as e:
            logger.error(f"❌ Failed to format option symbol: {e}")
            return ""
    
    def submit_multileg_order(self, order: MultiLegOrder) -> Optional[Dict[str, Any]]:
        """Submit a multi-leg order to Alpaca"""
        if not self.api:
            logger.error("❌ Alpaca API not initialized")
            return None
        
        try:
            # Convert order to dictionary
            order_data = order.to_dict()
            
            # Validate ratios (GCD requirement)
            ratios = [int(leg['ratio_qty']) for leg in order_data['legs']]
            simplified_ratios = self._simplify_ratios(ratios)
            
            # Update legs with simplified ratios
            for i, leg in enumerate(order_data['legs']):
                leg['ratio_qty'] = str(simplified_ratios[i])
            
            logger.info(f"🚀 Submitting Multi-Leg Order: {json.dumps(order_data, indent=2)}")
            
            # Submit order via API
            response = self.api.submit_order(**order_data)
            
            logger.info(f"✅ Multi-Leg Order submitted successfully: {response.id}")
            return response._raw
            
        except Exception as e:
            logger.error(f"❌ Failed to submit Multi-Leg Order: {e}")
            return None
    
    def calculate_maintenance_margin(self, legs: List[OptionLeg], underlying_price: float) -> float:
        """
        Calculate maintenance margin using Universal Spread Rule
        Based on theoretical maximum loss across all price points
        """
        try:
            # Get all strike prices from legs
            strikes = []
            for leg in legs:
                strike = self._extract_strike_from_symbol(leg.symbol)
                if strike:
                    strikes.append(strike)
            
            if not strikes:
                return 0.0
            
            # Add current underlying price as evaluation point
            strikes.append(underlying_price)
            strikes = sorted(set(strikes))
            
            max_loss = 0.0
            
            # Evaluate payoff at each strike price
            for price in strikes:
                total_payoff = 0.0
                
                for leg in legs:
                    payoff = self._calculate_leg_payoff(leg, price)
                    total_payoff += payoff
                
                # Track maximum loss (most negative payoff)
                if total_payoff < max_loss:
                    max_loss = total_payoff
            
            # Maintenance margin is absolute value of maximum loss
            margin = abs(max_loss)
            
            logger.info(f"📊 Calculated maintenance margin: ${margin:.2f}")
            return margin
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate maintenance margin: {e}")
            return 0.0
    
    def _extract_strike_from_symbol(self, symbol: str) -> Optional[float]:
        """Extract strike price from option symbol"""
        try:
            # Option symbol format: AAPL250117C00200000
            # Last 8 digits are strike * 1000
            strike_str = symbol[-8:]
            strike = int(strike_str) / 1000.0
            return strike
        except:
            return None
    
    def _calculate_leg_payoff(self, leg: OptionLeg, underlying_price: float) -> float:
        """Calculate payoff for a single leg at given underlying price"""
        try:
            strike = self._extract_strike_from_symbol(leg.symbol)
            if not strike:
                return 0.0
            
            # Determine if it's a call or put
            is_call = 'C' in leg.symbol
            is_long = leg.side == 'buy'
            
            # Calculate intrinsic value
            if is_call:
                intrinsic = max(0, underlying_price - strike)
            else:  # put
                intrinsic = max(0, strike - underlying_price)
            
            # Apply position direction
            if is_long:
                payoff = intrinsic
            else:  # short
                payoff = -intrinsic
            
            # Apply ratio quantity
            ratio = int(leg.ratio_qty)
            payoff *= ratio * 100  # Options multiplier
            
            return payoff
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate leg payoff: {e}")
            return 0.0

    def create_straddle(self, symbol: str, strike: float, expiration: str,
                       limit_price: float, qty: int = 1, is_long: bool = True) -> MultiLegOrder:
        """
        Create a Straddle (Long or Short)
        Long: Buy call + buy put at same strike
        Short: Sell call + sell put at same strike
        """
        try:
            call_symbol = self._format_option_symbol(symbol, expiration, 'C', strike)
            put_symbol = self._format_option_symbol(symbol, expiration, 'P', strike)

            if is_long:
                side = "buy"
                intent = "buy_to_open"
                strategy_name = "Long Straddle"
            else:
                side = "sell"
                intent = "sell_to_open"
                strategy_name = "Short Straddle"

            legs = [
                OptionLeg(
                    symbol=call_symbol,
                    side=side,
                    ratio_qty="1",
                    position_intent=intent
                ),
                OptionLeg(
                    symbol=put_symbol,
                    side=side,
                    ratio_qty="1",
                    position_intent=intent
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created {strategy_name}: Strike {strike}")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Straddle: {e}")
            return None

    def create_strangle(self, symbol: str, call_strike: float, put_strike: float,
                       expiration: str, limit_price: float, qty: int = 1, is_long: bool = True) -> MultiLegOrder:
        """
        Create a Strangle (Long or Short)
        Long: Buy OTM call + buy OTM put
        Short: Sell OTM call + sell OTM put
        """
        try:
            call_symbol = self._format_option_symbol(symbol, expiration, 'C', call_strike)
            put_symbol = self._format_option_symbol(symbol, expiration, 'P', put_strike)

            if is_long:
                side = "buy"
                intent = "buy_to_open"
                strategy_name = "Long Strangle"
            else:
                side = "sell"
                intent = "sell_to_open"
                strategy_name = "Short Strangle"

            legs = [
                OptionLeg(
                    symbol=call_symbol,
                    side=side,
                    ratio_qty="1",
                    position_intent=intent
                ),
                OptionLeg(
                    symbol=put_symbol,
                    side=side,
                    ratio_qty="1",
                    position_intent=intent
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created {strategy_name}: Call {call_strike}, Put {put_strike}")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Strangle: {e}")
            return None

    def create_iron_butterfly(self, symbol: str, lower_strike: float, middle_strike: float,
                             higher_strike: float, expiration: str, limit_price: float,
                             qty: int = 1) -> MultiLegOrder:
        """
        Create an Iron Butterfly
        Sell straddle at middle strike + buy protective wings
        """
        try:
            # Format option symbols
            lower_put_symbol = self._format_option_symbol(symbol, expiration, 'P', lower_strike)
            middle_call_symbol = self._format_option_symbol(symbol, expiration, 'C', middle_strike)
            middle_put_symbol = self._format_option_symbol(symbol, expiration, 'P', middle_strike)
            higher_call_symbol = self._format_option_symbol(symbol, expiration, 'C', higher_strike)

            legs = [
                # Buy protective put
                OptionLeg(
                    symbol=lower_put_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                ),
                # Sell put at middle strike
                OptionLeg(
                    symbol=middle_put_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                # Sell call at middle strike
                OptionLeg(
                    symbol=middle_call_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                # Buy protective call
                OptionLeg(
                    symbol=higher_call_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created Iron Butterfly: {lower_strike}/{middle_strike}/{higher_strike}")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Iron Butterfly: {e}")
            return None

    def create_covered_call(self, symbol: str, strike: float, expiration: str,
                           limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Create a Covered Call
        Own 100 shares + sell call option
        """
        try:
            call_symbol = self._format_option_symbol(symbol, expiration, 'C', strike)

            legs = [
                # Long stock position (100 shares per contract)
                OptionLeg(
                    symbol=symbol,  # Underlying stock symbol
                    side="buy",
                    ratio_qty=str(100 * qty),  # 100 shares per option contract
                    position_intent="buy_to_open"
                ),
                # Short call option
                OptionLeg(
                    symbol=call_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created Covered Call: {symbol} + {strike} Call")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Covered Call: {e}")
            return None

    def roll_spread_strike(self, symbol: str, old_lower_strike: float, old_higher_strike: float,
                          new_lower_strike: float, new_higher_strike: float, expiration: str,
                          option_type: str, limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Roll a spread to different strikes
        Close existing spread and open new spread in one order
        """
        try:
            # Format option symbols for old positions (to close)
            old_lower_symbol = self._format_option_symbol(symbol, expiration, option_type, old_lower_strike)
            old_higher_symbol = self._format_option_symbol(symbol, expiration, option_type, old_higher_strike)

            # Format option symbols for new positions (to open)
            new_lower_symbol = self._format_option_symbol(symbol, expiration, option_type, new_lower_strike)
            new_higher_symbol = self._format_option_symbol(symbol, expiration, option_type, new_higher_strike)

            legs = [
                # Close old spread
                OptionLeg(
                    symbol=old_lower_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_close"
                ),
                OptionLeg(
                    symbol=old_higher_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_close"
                ),
                # Open new spread
                OptionLeg(
                    symbol=new_lower_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                OptionLeg(
                    symbol=new_higher_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created Roll Spread: {old_lower_strike}/{old_higher_strike} -> {new_lower_strike}/{new_higher_strike}")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Roll Spread: {e}")
            return None

    def roll_spread_expiration(self, symbol: str, lower_strike: float, higher_strike: float,
                              old_expiration: str, new_expiration: str, option_type: str,
                              limit_price: float, qty: int = 1) -> MultiLegOrder:
        """
        Roll a spread to different expiration date
        Close existing spread and open new spread with different expiration
        """
        try:
            # Format option symbols for old positions (to close)
            old_lower_symbol = self._format_option_symbol(symbol, old_expiration, option_type, lower_strike)
            old_higher_symbol = self._format_option_symbol(symbol, old_expiration, option_type, higher_strike)

            # Format option symbols for new positions (to open)
            new_lower_symbol = self._format_option_symbol(symbol, new_expiration, option_type, lower_strike)
            new_higher_symbol = self._format_option_symbol(symbol, new_expiration, option_type, higher_strike)

            legs = [
                # Close old spread
                OptionLeg(
                    symbol=old_lower_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_close"
                ),
                OptionLeg(
                    symbol=old_higher_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_close"
                ),
                # Open new spread
                OptionLeg(
                    symbol=new_lower_symbol,
                    side="sell",
                    ratio_qty="1",
                    position_intent="sell_to_open"
                ),
                OptionLeg(
                    symbol=new_higher_symbol,
                    side="buy",
                    ratio_qty="1",
                    position_intent="buy_to_open"
                )
            ]

            order = MultiLegOrder(
                qty=str(qty),
                limit_price=str(limit_price),
                legs=legs
            )

            logger.info(f"✅ Created Roll Expiration: {old_expiration} -> {new_expiration}")
            return order

        except Exception as e:
            logger.error(f"❌ Failed to create Roll Expiration: {e}")
            return None

    def calculate_order_cost_basis(self, legs: List[OptionLeg], underlying_price: float,
                                  option_prices: Dict[str, float]) -> float:
        """
        Calculate order cost basis = maintenance margin + net option price
        """
        try:
            # Calculate maintenance margin
            maintenance_margin = self.calculate_maintenance_margin(legs, underlying_price)

            # Calculate net option price
            net_option_price = 0.0
            for leg in legs:
                if leg.symbol in option_prices:
                    price = option_prices[leg.symbol]
                    ratio = int(leg.ratio_qty)

                    if leg.side == "buy":
                        net_option_price -= price * ratio * 100  # Debit (cost)
                    else:  # sell
                        net_option_price += price * ratio * 100  # Credit (income)

            # Cost basis = margin + net price (credit reduces cost)
            cost_basis = maintenance_margin + abs(net_option_price)

            logger.info(f"💰 Cost Basis: Margin ${maintenance_margin:.2f} + Net Price ${abs(net_option_price):.2f} = ${cost_basis:.2f}")
            return cost_basis

        except Exception as e:
            logger.error(f"❌ Failed to calculate cost basis: {e}")
            return 0.0

    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a submitted multi-leg order"""
        if not self.api:
            logger.error("❌ Alpaca API not initialized")
            return None

        try:
            order = self.api.get_order(order_id)
            return order._raw
        except Exception as e:
            logger.error(f"❌ Failed to get order status: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """Cancel a pending multi-leg order"""
        if not self.api:
            logger.error("❌ Alpaca API not initialized")
            return False

        try:
            self.api.cancel_order(order_id)
            logger.info(f"✅ Order {order_id} cancelled successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to cancel order {order_id}: {e}")
            return False

    def get_positions(self) -> List[Dict[str, Any]]:
        """Get all current positions"""
        if not self.api:
            logger.error("❌ Alpaca API not initialized")
            return []

        try:
            positions = self.api.list_positions()
            return [pos._raw for pos in positions]
        except Exception as e:
            logger.error(f"❌ Failed to get positions: {e}")
            return []

    def validate_multileg_order(self, order: MultiLegOrder) -> Tuple[bool, str]:
        """
        Validate multi-leg order according to Alpaca requirements
        """
        try:
            # Check if all legs are covered (no naked shorts in same order)
            short_legs = [leg for leg in order.legs if leg.side == "sell"]

            # For now, allow only covered strategies
            if len(short_legs) > 2:
                # Check if it's a valid multi-leg strategy
                if not self._is_valid_strategy(order.legs):
                    return False, "Multi-leg order contains uncovered short legs"

            # Validate ratio quantities (GCD requirement)
            ratios = [int(leg.ratio_qty) for leg in order.legs]
            if len(ratios) > 1:
                gcd = ratios[0]
                for ratio in ratios[1:]:
                    gcd = self._gcd(gcd, ratio)
                if gcd > 1:
                    return False, f"Leg ratios must be in simplest form (GCD={gcd})"

            # Validate option symbols
            for leg in order.legs:
                if not self._is_valid_option_symbol(leg.symbol):
                    return False, f"Invalid option symbol: {leg.symbol}"

            return True, "Order validation passed"

        except Exception as e:
            logger.error(f"❌ Order validation failed: {e}")
            return False, f"Validation error: {e}"

    def _is_valid_strategy(self, legs: List[OptionLeg]) -> bool:
        """Check if the legs form a valid covered strategy"""
        # This is a simplified validation - in practice, you'd implement
        # more sophisticated strategy recognition
        return len(legs) <= 4  # Most common strategies have 4 legs or fewer

    def _is_valid_option_symbol(self, symbol: str) -> bool:
        """Validate option symbol format"""
        try:
            # Basic validation for OCC format
            if len(symbol) < 15:  # Minimum length for option symbol
                return True  # Could be stock symbol for covered call

            # Check if it follows OCC format: SYMBOL + YYMMDD + C/P + 8-digit strike
            if symbol[-9] in ['C', 'P'] and symbol[-8:].isdigit():
                return True

            return False
        except:
            return False
