<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Leg Options Trading - AI Options Trader</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> AI Options Trader
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/trading">Paper Trading</a>
                <a class="nav-link" href="/auto-trading">Auto Trading</a>
                <a class="nav-link" href="/hedging">Hedging</a>
                <a class="nav-link active" href="/multileg">Multi-Leg</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-layer-group"></i> Multi-Leg Options Trading</h1>
                <p class="lead">Advanced options strategies with Alpaca Level 3 Trading</p>
            </div>
        </div>

        <!-- Strategy Builder -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Strategy Builder</h5>
                    </div>
                    <div class="card-body">
                        <form id="strategyForm">
                            <div class="mb-3">
                                <label for="strategyType" class="form-label">Strategy Type</label>
                                <select class="form-select" id="strategyType" required>
                                    <option value="">Select Strategy</option>
                                    <option value="long_call_spread">Long Call Spread</option>
                                    <option value="long_put_spread">Long Put Spread</option>
                                    <option value="iron_condor">Iron Condor</option>
                                    <option value="straddle">Straddle</option>
                                    <option value="iron_butterfly">Iron Butterfly</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="symbol" class="form-label">Symbol</label>
                                <input type="text" class="form-control" id="symbol" placeholder="AAPL" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expiration" class="form-label">Expiration Date</label>
                                <input type="date" class="form-control" id="expiration" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="limitPrice" class="form-label">Limit Price</label>
                                <input type="number" class="form-control" id="limitPrice" step="0.01" placeholder="1.00" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" value="1" min="1" required>
                            </div>
                            
                            <!-- Dynamic fields based on strategy type -->
                            <div id="dynamicFields"></div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Create Order
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Active Orders</h5>
                    </div>
                    <div class="card-body">
                        <div id="activeOrders">
                            <p class="text-muted">No active orders</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Preview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye"></i> Order Preview</h5>
                    </div>
                    <div class="card-body">
                        <div id="orderPreview">
                            <p class="text-muted">Configure strategy above to see order preview</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Strategy Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Strategy Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>Long Call Spread</h6>
                                <p class="small">Buy lower strike call, sell higher strike call. Bullish strategy with limited profit and loss.</p>
                            </div>
                            <div class="col-md-4">
                                <h6>Iron Condor</h6>
                                <p class="small">Sell put spread + sell call spread. Neutral strategy that profits from low volatility.</p>
                            </div>
                            <div class="col-md-4">
                                <h6>Straddle</h6>
                                <p class="small">Buy call + put at same strike. Profits from high volatility in either direction.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Strategy type change handler
        document.getElementById('strategyType').addEventListener('change', function() {
            const strategyType = this.value;
            const dynamicFields = document.getElementById('dynamicFields');
            dynamicFields.innerHTML = '';
            
            if (strategyType === 'long_call_spread' || strategyType === 'long_put_spread') {
                dynamicFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Lower Strike</label>
                        <input type="number" class="form-control" id="lowerStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Higher Strike</label>
                        <input type="number" class="form-control" id="higherStrike" step="0.50" required>
                    </div>
                `;
            } else if (strategyType === 'iron_condor') {
                dynamicFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Put Lower Strike</label>
                        <input type="number" class="form-control" id="putLowerStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Put Higher Strike</label>
                        <input type="number" class="form-control" id="putHigherStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Call Lower Strike</label>
                        <input type="number" class="form-control" id="callLowerStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Call Higher Strike</label>
                        <input type="number" class="form-control" id="callHigherStrike" step="0.50" required>
                    </div>
                `;
            } else if (strategyType === 'straddle') {
                dynamicFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Strike Price</label>
                        <input type="number" class="form-control" id="strike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Position Type</label>
                        <select class="form-select" id="isLong" required>
                            <option value="true">Long (Buy)</option>
                            <option value="false">Short (Sell)</option>
                        </select>
                    </div>
                `;
            } else if (strategyType === 'iron_butterfly') {
                dynamicFields.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Lower Strike</label>
                        <input type="number" class="form-control" id="lowerStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Middle Strike</label>
                        <input type="number" class="form-control" id="middleStrike" step="0.50" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Higher Strike</label>
                        <input type="number" class="form-control" id="higherStrike" step="0.50" required>
                    </div>
                `;
            }
        });

        // Form submission handler
        document.getElementById('strategyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const strategyType = document.getElementById('strategyType').value;
            
            const orderData = {
                strategy_type: strategyType,
                symbol: document.getElementById('symbol').value.toUpperCase(),
                expiration: document.getElementById('expiration').value,
                limit_price: parseFloat(document.getElementById('limitPrice').value),
                qty: parseInt(document.getElementById('quantity').value)
            };
            
            // Add strategy-specific fields
            if (strategyType === 'long_call_spread' || strategyType === 'long_put_spread') {
                orderData.lower_strike = parseFloat(document.getElementById('lowerStrike').value);
                orderData.higher_strike = parseFloat(document.getElementById('higherStrike').value);
            } else if (strategyType === 'iron_condor') {
                orderData.put_lower_strike = parseFloat(document.getElementById('putLowerStrike').value);
                orderData.put_higher_strike = parseFloat(document.getElementById('putHigherStrike').value);
                orderData.call_lower_strike = parseFloat(document.getElementById('callLowerStrike').value);
                orderData.call_higher_strike = parseFloat(document.getElementById('callHigherStrike').value);
            } else if (strategyType === 'straddle') {
                orderData.strike = parseFloat(document.getElementById('strike').value);
                orderData.is_long = document.getElementById('isLong').value === 'true';
            } else if (strategyType === 'iron_butterfly') {
                orderData.lower_strike = parseFloat(document.getElementById('lowerStrike').value);
                orderData.middle_strike = parseFloat(document.getElementById('middleStrike').value);
                orderData.higher_strike = parseFloat(document.getElementById('higherStrike').value);
            }
            
            // Submit order
            fetch('/api/multileg/create-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Order created successfully!');
                    document.getElementById('orderPreview').innerHTML = `
                        <pre>${JSON.stringify(data.order, null, 2)}</pre>
                    `;
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error creating order');
            });
        });

        // Set default expiration date (30 days from now)
        const defaultExpiration = new Date();
        defaultExpiration.setDate(defaultExpiration.getDate() + 30);
        document.getElementById('expiration').value = defaultExpiration.toISOString().split('T')[0];
    </script>
</body>
</html>
