import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import numpy as np

from config import Config

logger = logging.getLogger(__name__)

class RiskManager:
    """Manages risk for options strategies"""
    
    def __init__(self):
        self.max_loss_per_trade = Config.MAX_LOSS_PER_TRADE
        self.max_daily_drawdown = Config.MAX_DAILY_DRAWDOWN
        self.max_trades_per_sector = Config.MAX_TRADES_PER_SECTOR
        self.max_bid_ask_spread = Config.MAX_BID_ASK_SPREAD
        
        # Portfolio tracking (in-memory for demo)
        self.current_trades = []
        self.daily_pnl = 0
        self.max_portfolio_value = 100000  # Assume $100k portfolio
    
    def filter_strategies(self, strategies: List[Dict]) -> List[Dict]:
        """
        Filter strategies based on risk management rules
        """
        filtered_strategies = []
        
        for strategy in strategies:
            if self._passes_risk_checks(strategy):
                filtered_strategies.append(strategy)
            else:
                logger.debug(f"Strategy filtered out: {strategy.get('symbol')} {strategy.get('strategy')}")
        
        return filtered_strategies
    
    def _passes_risk_checks(self, strategy: Dict) -> bool:
        """
        Check if strategy passes all risk management criteria
        """
        try:
            # Check 1: Maximum loss per trade (TEMPORARILY RELAXED)
            max_loss = strategy.get('max_loss', 0)
            max_allowed_loss = self.max_portfolio_value * 0.10  # Temporarily increase to 10% instead of 2%

            if max_loss > max_allowed_loss:
                logger.debug(f"Trade rejected: Max loss ${max_loss} exceeds limit ${max_allowed_loss}")
                return False
            
            # Check 2: Bid-ask spread
            bid_ask_spread = strategy.get('bid_ask_spread', 0)
            premium = strategy.get('premium', 1)
            
            if premium > 0:
                spread_percentage = bid_ask_spread / premium
                if spread_percentage > self.max_bid_ask_spread:
                    logger.debug(f"Trade rejected: Bid-ask spread {spread_percentage:.2%} exceeds {self.max_bid_ask_spread:.2%}")
                    return False
            
            # Check 3: Minimum volume requirement (TEMPORARILY RELAXED)
            volume = strategy.get('volume', 0)
            min_volume = 10  # Temporarily reduce to 10 instead of Config.MIN_OPTION_VOLUME (50)
            if volume < min_volume:
                logger.debug(f"Trade rejected: Volume {volume} below minimum {min_volume}")
                return False
            
            # Check 4: Days to expiry limits
            dte = strategy.get('dte', 0)
            if dte < Config.MIN_DTE or dte > Config.MAX_DTE:
                logger.debug(f"Trade rejected: DTE {dte} outside range {Config.MIN_DTE}-{Config.MAX_DTE}")
                return False
            
            # Check 5: Expected value threshold
            ev = strategy.get('ev', 0)
            if ev <= 0:
                logger.debug(f"Trade rejected: Negative or zero EV {ev}")
                return False
            
            # Check 6: Probability of profit threshold (TEMPORARILY RELAXED)
            pop = strategy.get('pop', 0)
            if pop < 0.2:  # Temporarily reduce to 20% POP instead of 40%
                logger.debug(f"Trade rejected: POP {pop:.2%} below 20%")
                return False

            # Check 7: Risk-reward ratio (TEMPORARILY RELAXED)
            risk_reward = strategy.get('risk_reward', 0)
            if risk_reward < 0.1:  # Temporarily reduce to 10% return on risk instead of 30%
                logger.debug(f"Trade rejected: Risk-reward {risk_reward:.2%} below 10%")
                return False
            
            # Check 8: Sector concentration (placeholder)
            # In production, you'd track actual sector exposure
            symbol = strategy.get('symbol', '')
            current_sector_count = self._count_sector_exposure(symbol)
            if current_sector_count >= self.max_trades_per_sector:
                logger.debug(f"Trade rejected: Max trades per sector reached for {symbol}")
                return False
            
            # Check 9: Portfolio heat (total risk exposure)
            if not self._check_portfolio_heat(max_loss):
                logger.debug(f"Trade rejected: Portfolio heat limit exceeded")
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Risk check failed: {e}")
            return False
    
    def _count_sector_exposure(self, symbol: str) -> int:
        """
        Count current trades in the same sector (simplified)
        In production, this would use actual sector mapping
        """
        # Simplified sector mapping
        tech_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'AMD', 'TSLA']
        finance_symbols = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']
        
        if symbol in tech_symbols:
            sector = 'tech'
        elif symbol in finance_symbols:
            sector = 'finance'
        else:
            sector = 'other'
        
        # Count current trades in same sector
        sector_count = 0
        for trade in self.current_trades:
            trade_symbol = trade.get('symbol', '')
            if trade_symbol in tech_symbols and sector == 'tech':
                sector_count += 1
            elif trade_symbol in finance_symbols and sector == 'finance':
                sector_count += 1
            elif trade_symbol not in tech_symbols + finance_symbols and sector == 'other':
                sector_count += 1
        
        return sector_count
    
    def _check_portfolio_heat(self, additional_risk: float) -> bool:
        """
        Check if adding this trade would exceed portfolio heat limits
        """
        current_risk = sum(trade.get('max_loss', 0) for trade in self.current_trades)
        total_risk = current_risk + additional_risk
        max_portfolio_risk = self.max_portfolio_value * 0.15  # Max 15% of portfolio at risk
        
        return total_risk <= max_portfolio_risk
    
    def calculate_position_size(self, strategy: Dict, portfolio_value: float) -> int:
        """
        Calculate appropriate position size based on risk management
        """
        try:
            max_loss = strategy.get('max_loss', 0)
            if max_loss <= 0:
                return 0
            
            # Maximum risk per trade
            max_trade_risk = portfolio_value * self.max_loss_per_trade
            
            # Calculate number of contracts
            contracts = int(max_trade_risk / max_loss)
            
            # Ensure minimum of 1 contract if trade passes other filters
            return max(1, contracts)
            
        except Exception as e:
            logger.warning(f"Position sizing failed: {e}")
            return 1
    
    def validate_trade_entry(self, strategy: Dict) -> Dict[str, any]:
        """
        Validate trade before entry and return entry parameters
        """
        try:
            # Basic validation
            if not self._passes_risk_checks(strategy):
                return {
                    'approved': False,
                    'reason': 'Failed risk management checks'
                }
            
            # Calculate position size
            position_size = self.calculate_position_size(strategy, self.max_portfolio_value)
            
            if position_size <= 0:
                return {
                    'approved': False,
                    'reason': 'Position size too small'
                }
            
            # Check for conflicting positions
            conflict = self._check_position_conflicts(strategy)
            if conflict:
                return {
                    'approved': False,
                    'reason': f'Position conflict: {conflict}'
                }
            
            # Calculate entry parameters
            entry_params = self._calculate_entry_parameters(strategy, position_size)
            
            return {
                'approved': True,
                'position_size': position_size,
                'entry_params': entry_params,
                'risk_metrics': self._calculate_risk_metrics(strategy, position_size)
            }
            
        except Exception as e:
            logger.error(f"Trade validation failed: {e}")
            return {
                'approved': False,
                'reason': f'Validation error: {e}'
            }
    
    def _check_position_conflicts(self, strategy: Dict) -> Optional[str]:
        """
        Check for conflicting positions in the same underlying
        """
        symbol = strategy.get('symbol', '')
        
        for trade in self.current_trades:
            if trade.get('symbol') == symbol:
                # Check for opposite strategies
                current_strategy = trade.get('strategy', '')
                new_strategy = strategy.get('strategy', '')
                
                # Define conflicting strategies
                conflicts = {
                    'naked_call': ['naked_put'],
                    'naked_put': ['naked_call'],
                    'call_spread': ['put_spread'],
                    'put_spread': ['call_spread']
                }
                
                if current_strategy in conflicts.get(new_strategy, []):
                    return f"Conflicting strategy: {current_strategy} vs {new_strategy}"
        
        return None
    
    def _calculate_entry_parameters(self, strategy: Dict, position_size: int) -> Dict:
        """
        Calculate specific entry parameters
        """
        return {
            'contracts': position_size,
            'max_loss_total': strategy.get('max_loss', 0) * position_size,
            'max_gain_total': strategy.get('max_gain', 0) * position_size if strategy.get('max_gain', 0) != 999999 else 'Unlimited',
            'breakeven': strategy.get('breakeven'),
            'target_profit': strategy.get('max_loss', 0) * position_size * 2,  # 2:1 target
            'stop_loss': strategy.get('max_loss', 0) * position_size * 0.5,  # 50% stop
            'time_stop': strategy.get('dte', 7) // 2  # Close at 50% time decay
        }
    
    def _calculate_risk_metrics(self, strategy: Dict, position_size: int) -> Dict:
        """
        Calculate comprehensive risk metrics
        """
        max_loss = strategy.get('max_loss', 0) * position_size
        
        return {
            'max_loss_dollars': max_loss,
            'max_loss_percentage': max_loss / self.max_portfolio_value * 100,
            'expected_value': strategy.get('ev', 0) * position_size,
            'probability_of_profit': strategy.get('pop', 0),
            'risk_reward_ratio': strategy.get('risk_reward', 0),
            'portfolio_heat': (sum(t.get('max_loss', 0) for t in self.current_trades) + max_loss) / self.max_portfolio_value * 100,
            'delta_exposure': strategy.get('delta', 0) * position_size * 100,
            'theta_decay': strategy.get('theta', 0) * position_size * 100,
            'vega_exposure': strategy.get('vega', 0) * position_size * 100
        }
    
    def add_trade(self, strategy: Dict, position_size: int):
        """
        Add trade to current portfolio tracking
        """
        trade = strategy.copy()
        trade['position_size'] = position_size
        trade['entry_time'] = datetime.now().isoformat()
        trade['status'] = 'open'
        
        self.current_trades.append(trade)
        logger.info(f"Added trade: {trade['symbol']} {trade['strategy']} x{position_size}")
    
    def remove_trade(self, trade_id: str):
        """
        Remove trade from current portfolio
        """
        self.current_trades = [t for t in self.current_trades if t.get('id') != trade_id]
    
    def get_portfolio_summary(self) -> Dict:
        """
        Get current portfolio risk summary
        """
        if not self.current_trades:
            return {
                'total_trades': 0,
                'total_risk': 0,
                'portfolio_heat': 0,
                'net_delta': 0,
                'net_theta': 0,
                'sectors': {}
            }
        
        total_risk = sum(t.get('max_loss', 0) * t.get('position_size', 1) for t in self.current_trades)
        net_delta = sum(t.get('delta', 0) * t.get('position_size', 1) * 100 for t in self.current_trades)
        net_theta = sum(t.get('theta', 0) * t.get('position_size', 1) * 100 for t in self.current_trades)
        
        # Sector breakdown
        sectors = {}
        for trade in self.current_trades:
            symbol = trade.get('symbol', '')
            # Simplified sector assignment
            if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'AMD', 'TSLA']:
                sector = 'Technology'
            elif symbol in ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C']:
                sector = 'Finance'
            else:
                sector = 'Other'
            
            sectors[sector] = sectors.get(sector, 0) + 1
        
        return {
            'total_trades': len(self.current_trades),
            'total_risk': total_risk,
            'portfolio_heat': total_risk / self.max_portfolio_value * 100,
            'net_delta': net_delta,
            'net_theta': net_theta,
            'sectors': sectors,
            'daily_pnl': self.daily_pnl
        }
    
    def check_exit_conditions(self, trade: Dict, current_market_data: Dict) -> Dict:
        """
        Check if trade should be exited based on risk management rules
        """
        try:
            current_price = current_market_data.get('price', 0)
            entry_time = datetime.fromisoformat(trade.get('entry_time', datetime.now().isoformat()))
            days_held = (datetime.now() - entry_time).days
            
            exit_signals = []
            
            # Time-based exit (50% of time to expiry)
            dte_remaining = trade.get('dte', 7) - days_held
            if dte_remaining <= trade.get('dte', 7) // 2:
                exit_signals.append('time_decay')
            
            # Profit target (50% of max gain for credit strategies)
            if trade.get('strategy') in ['iron_condor', 'naked_put', 'naked_call']:
                # For credit strategies, exit at 50% profit
                exit_signals.append('profit_target_50')
            
            # Stop loss (varies by strategy)
            # Implementation would depend on current option prices
            
            return {
                'should_exit': len(exit_signals) > 0,
                'exit_reasons': exit_signals,
                'days_held': days_held,
                'dte_remaining': dte_remaining
            }
            
        except Exception as e:
            logger.error(f"Exit condition check failed: {e}")
            return {'should_exit': False, 'exit_reasons': []}
