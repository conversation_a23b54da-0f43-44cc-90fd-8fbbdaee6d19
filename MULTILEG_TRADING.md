# Multi-Leg Options Trading Implementation

## Overview

This implementation provides comprehensive support for Alpaca's Level 3 Options Trading with multi-leg strategies. It follows the official Alpaca documentation and implements advanced options strategies with proper risk management and margin calculations.

## Features Implemented

### 🎯 Supported Strategies

1. **Long Call Spread (Bull Call Spread)**
   - Buy lower strike call, sell higher strike call
   - Limited profit and loss potential
   - Bullish strategy

2. **Long Put Spread (Bear Put Spread)**
   - Buy higher strike put, sell lower strike put
   - Limited profit and loss potential
   - Bearish strategy

3. **Iron Condor**
   - Combination of put spread and call spread
   - Neutral strategy that profits from low volatility
   - 4-leg strategy with defined risk

4. **Straddle (Long/Short)**
   - Buy/sell call and put at same strike
   - Profits from high volatility (long) or low volatility (short)

5. **Iron Butterfly**
   - Sell straddle with protective wings
   - Neutral strategy with limited risk

6. **Covered Call**
   - Own 100 shares + sell call option
   - Income generation strategy

### 🔧 Technical Features

#### Universal Spread Rule Margin Calculation
- Implements Alpaca's advanced margin calculation
- Uses piecewise linear payoff functions
- Calculates theoretical maximum loss across all price points
- More capital efficient than traditional margin calculations

#### Order Validation
- **GCD Requirement**: Ensures leg ratios are in simplest form
- **Strategy Validation**: Checks for covered vs uncovered legs
- **Symbol Validation**: Validates OCC option symbol format

#### Risk Management
- Real-time margin calculations
- Cost basis calculations (margin + net option price)
- Position payoff analysis at multiple price points

## API Endpoints

### Multi-Leg Order Management

```
POST /api/multileg/submit
- Submit a strategy as a multi-leg order

GET /api/multileg/status/<order_id>
- Get status of a multi-leg order

POST /api/multileg/cancel/<order_id>
- Cancel a pending multi-leg order

POST /api/multileg/create-order
- Create a multi-leg order from parameters
```

### Example API Usage

#### Create Long Call Spread
```json
POST /api/multileg/create-order
{
  "strategy_type": "long_call_spread",
  "symbol": "AAPL",
  "lower_strike": 190.0,
  "higher_strike": 210.0,
  "expiration": "2025-01-17",
  "limit_price": 1.00,
  "qty": 1
}
```

#### Create Iron Condor
```json
POST /api/multileg/create-order
{
  "strategy_type": "iron_condor",
  "symbol": "SPY",
  "put_lower_strike": 590.0,
  "put_higher_strike": 595.0,
  "call_lower_strike": 605.0,
  "call_higher_strike": 610.0,
  "expiration": "2025-01-17",
  "limit_price": 1.80,
  "qty": 1
}
```

## Code Structure

### Core Classes

#### `AlpacaMultiLegTrader`
Main class for multi-leg trading functionality:
- Order creation methods for each strategy type
- Margin calculation using Universal Spread Rule
- Order validation and submission
- Integration with Alpaca API

#### `MultiLegOrder`
Data class representing a complete multi-leg order:
- Order parameters (price, quantity, time in force)
- List of option legs
- Conversion to API format

#### `OptionLeg`
Data class representing a single leg:
- Option symbol (OCC format)
- Side (buy/sell)
- Ratio quantity
- Position intent (buy_to_open, sell_to_open, etc.)

### Integration Points

#### Strategy Evaluator Integration
- `WorkingStrategyEvaluator` can convert strategies to multi-leg orders
- Automatic order creation from scan results
- Seamless integration with existing strategy logic

#### Web Interface
- `/multileg` route provides interactive strategy builder
- Real-time order preview and validation
- Support for all implemented strategy types

## Alpaca Level 3 Trading Compliance

### Order Structure
```json
{
  "order_class": "mleg",
  "qty": "1",
  "type": "limit",
  "limit_price": "1.00",
  "time_in_force": "day",
  "legs": [
    {
      "symbol": "AAPL250117C00190000",
      "ratio_qty": "1",
      "side": "buy",
      "position_intent": "buy_to_open"
    }
  ]
}
```

### Margin Calculation Example
For an Iron Condor with strikes 590/595/605/610:
1. Calculate payoff at each strike price
2. Find maximum loss across all prices
3. Margin = absolute value of maximum loss
4. Result: More efficient than sum of individual spreads

### Cost Basis Calculation
```
Cost Basis = Maintenance Margin + |Net Option Price|
```

Example for Call Credit Spread:
- Maintenance Margin: $1,000
- Net Credit: $500
- Cost Basis: $1,000 + $500 = $1,500

## Testing

Run the test suite to verify functionality:
```bash
python test_multileg.py
```

Tests cover:
- ✅ Multi-leg order creation
- ✅ Order validation
- ✅ Margin calculation
- ✅ Symbol formatting
- ✅ Ratio simplification
- ✅ Strategy integration

## Usage Examples

### Basic Usage
```python
from scanner.alpaca_multileg_trader import AlpacaMultiLegTrader

trader = AlpacaMultiLegTrader()

# Create Iron Condor
order = trader.create_iron_condor(
    symbol="SPY",
    put_lower_strike=590.0,
    put_higher_strike=595.0,
    call_lower_strike=605.0,
    call_higher_strike=610.0,
    expiration="2025-01-17",
    limit_price=1.80
)

# Validate and submit
is_valid, message = trader.validate_multileg_order(order)
if is_valid:
    response = trader.submit_multileg_order(order)
```

### Strategy Integration
```python
from scanner.working_strategy_evaluator import WorkingStrategyEvaluator

evaluator = WorkingStrategyEvaluator()

# Convert strategy to multi-leg order
strategy = {
    'strategy_type': 'iron_condor',
    'symbol': 'SPY',
    # ... strategy parameters
}

order_id = evaluator.submit_strategy_as_multileg(strategy)
```

## Benefits

1. **Capital Efficiency**: Universal Spread Rule reduces margin requirements
2. **Risk Management**: Precise margin and payoff calculations
3. **Strategy Diversity**: Support for complex multi-leg strategies
4. **Compliance**: Full adherence to Alpaca Level 3 requirements
5. **Integration**: Seamless integration with existing scanner system

## Next Steps

1. Add more advanced strategies (calendars, diagonals, ratios)
2. Implement position rolling functionality
3. Add real-time Greeks calculations
4. Enhance risk monitoring and alerts
5. Add backtesting for multi-leg strategies
