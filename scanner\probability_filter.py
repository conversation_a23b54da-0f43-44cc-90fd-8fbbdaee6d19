"""
Advanced probability-based filtering system for options trading
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from scanner.options_calculator import OptionsCalculator

logger = logging.getLogger(__name__)

@dataclass
class ProbabilityThresholds:
    """Configuration for probability-based filtering thresholds"""
    min_pop: float = 0.2  # TEMPORARILY RELAXED: Minimum probability of profit (was 0.6)
    max_pop: float = 0.95  # Maximum probability of profit (avoid too good to be true)
    min_iv_percentile: float = 0.1  # TEMPORARILY RELAXED: Minimum IV percentile (was 0.3)
    max_iv_percentile: float = 0.9  # TEMPORARILY RELAXED: Maximum IV percentile (was 0.8)
    min_delta: float = 0.05  # TEMPORARILY RELAXED: Minimum delta for calls (was 0.15)
    max_delta: float = 0.95  # TEMPORARILY RELAXED: Maximum delta for calls (was 0.85)
    min_gamma: float = 0.001  # TEMPORARILY RELAXED: Minimum gamma (was 0.01)
    max_gamma: float = 1.0   # TEMPORARILY RELAXED: Maximum gamma (was 0.5)
    min_volume: int = 5     # TEMPORARILY RELAXED: Minimum daily volume (was 50)
    min_open_interest: int = 10  # TEMPORARILY RELAXED: Minimum open interest (was 100)
    min_bid_ask_spread_ratio: float = 0.01  # TEMPORARILY RELAXED: Min bid-ask spread (was 0.05)
    max_bid_ask_spread_ratio: float = 0.5  # TEMPORARILY RELAXED: Max bid-ask spread (was 0.15)
    min_time_value_pct: float = 0.01  # TEMPORARILY RELAXED: Minimum time value percentage (was 0.1)
    max_dte: int = 60        # TEMPORARILY RELAXED: Maximum days to expiration (was 45)
    min_dte: int = 1         # TEMPORARILY RELAXED: Minimum days to expiration (was 7)

class ProbabilityFilter:
    """Advanced probability-based filtering for options strategies"""
    
    def __init__(self, thresholds: Optional[ProbabilityThresholds] = None):
        self.thresholds = thresholds or ProbabilityThresholds()
        self.calculator = OptionsCalculator()
        
    def filter_options_by_probability(self, options_data: List[Dict], 
                                    current_price: float,
                                    historical_iv_data: Optional[Dict] = None) -> List[Dict]:
        """
        Filter options based on probability criteria
        """
        filtered_options = []
        
        for option in options_data:
            try:
                # Extract option data
                strike = option.get('strike', 0)
                bid = option.get('bid', 0)
                ask = option.get('ask', 0)
                volume = option.get('volume', 0)
                open_interest = option.get('open_interest', 0)
                dte = option.get('dte', 0)
                option_type = option.get('type', 'call')
                
                # Basic validation
                if not self._passes_basic_validation(strike, bid, ask, volume, open_interest, dte):
                    continue
                
                # Calculate mid price
                mid_price = (bid + ask) / 2 if bid > 0 and ask > 0 else option.get('last', 0)
                if mid_price <= 0:
                    continue
                
                # Calculate advanced metrics
                symbol_iv_history = historical_iv_data.get(option.get('symbol', ''), []) if historical_iv_data else []
                metrics = self.calculator.calculate_advanced_probability_metrics(
                    current_price, strike, mid_price, dte, option_type, symbol_iv_history
                )
                
                if not metrics:
                    continue
                
                # Apply probability filters
                if self._passes_probability_filters(metrics, bid, ask, mid_price):
                    # Add calculated metrics to option data
                    option.update(metrics)
                    option['mid_price'] = mid_price
                    option['bid_ask_spread'] = ask - bid
                    option['bid_ask_spread_ratio'] = (ask - bid) / mid_price if mid_price > 0 else 1
                    
                    # Calculate probability score
                    option['probability_score'] = self._calculate_probability_score(option, metrics)
                    
                    filtered_options.append(option)
                    
            except Exception as e:
                logger.debug(f"Error filtering option {option}: {e}")
                continue
        
        # Sort by probability score (highest first)
        return sorted(filtered_options, key=lambda x: x.get('probability_score', 0), reverse=True)
    
    def _passes_basic_validation(self, strike: float, bid: float, ask: float, 
                               volume: int, open_interest: int, dte: int) -> bool:
        """Basic validation checks"""
        return (
            strike > 0 and
            bid >= 0 and ask > bid and
            volume >= self.thresholds.min_volume and
            open_interest >= self.thresholds.min_open_interest and
            self.thresholds.min_dte <= dte <= self.thresholds.max_dte
        )
    
    def _passes_probability_filters(self, metrics: Dict, bid: float, ask: float, mid_price: float) -> bool:
        """Apply probability-based filters"""
        try:
            # Probability of profit filter
            pop = metrics.get('probability_of_profit', 0)
            if not (self.thresholds.min_pop <= pop <= self.thresholds.max_pop):
                return False
            
            # IV percentile filter
            iv_percentile = metrics.get('iv_percentile', 0.5)
            if not (self.thresholds.min_iv_percentile <= iv_percentile <= self.thresholds.max_iv_percentile):
                return False
            
            # Delta filter (absolute value for puts)
            delta = abs(metrics.get('delta', 0))
            if not (self.thresholds.min_delta <= delta <= self.thresholds.max_delta):
                return False
            
            # Gamma filter
            gamma = metrics.get('gamma', 0)
            if not (self.thresholds.min_gamma <= gamma <= self.thresholds.max_gamma):
                return False
            
            # Bid-ask spread filter
            spread_ratio = (ask - bid) / mid_price if mid_price > 0 else 1
            if not (self.thresholds.min_bid_ask_spread_ratio <= spread_ratio <= self.thresholds.max_bid_ask_spread_ratio):
                return False
            
            # Time value percentage filter
            time_value_pct = metrics.get('time_value_percentage', 0)
            if time_value_pct < self.thresholds.min_time_value_pct:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Error in probability filters: {e}")
            return False
    
    def _calculate_probability_score(self, option: Dict, metrics: Dict) -> float:
        """
        Calculate a comprehensive probability score for ranking options
        """
        try:
            score = 0.0
            
            # Base score from probability of profit (40% weight)
            pop = metrics.get('probability_of_profit', 0)
            score += pop * 0.4
            
            # Delta-adjusted POP bonus (20% weight)
            delta_adjusted_pop = metrics.get('delta_adjusted_pop', pop)
            score += delta_adjusted_pop * 0.2
            
            # IV percentile score (15% weight) - prefer moderate IV
            iv_percentile = metrics.get('iv_percentile', 0.5)
            iv_score = 1 - abs(iv_percentile - 0.6)  # Optimal around 60th percentile
            score += iv_score * 0.15
            
            # Liquidity score (10% weight)
            volume = option.get('volume', 0)
            open_interest = option.get('open_interest', 0)
            liquidity_score = min(1.0, (volume + open_interest) / 1000)
            score += liquidity_score * 0.1
            
            # Bid-ask spread score (10% weight) - tighter spreads are better
            spread_ratio = option.get('bid_ask_spread_ratio', 1)
            spread_score = max(0, 1 - spread_ratio * 10)  # Penalize wide spreads
            score += spread_score * 0.1
            
            # Time decay advantage (5% weight) - prefer options with reasonable theta
            theta = abs(metrics.get('theta', 0))
            theta_score = min(1.0, theta * 100)  # Normalize theta
            score += theta_score * 0.05
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.warning(f"Error calculating probability score: {e}")
            return 0.0
    
    def get_technical_confirmations(self, symbol: str, market_data: Dict) -> Dict:
        """
        Get technical indicator confirmations for probability filtering
        """
        try:
            confirmations = {
                'trend_confirmation': False,
                'volume_confirmation': False,
                'momentum_confirmation': False,
                'volatility_confirmation': False,
                'support_resistance_confirmation': False
            }
            
            # Volume confirmation
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume', 0)
            if avg_volume > 0:
                volume_ratio = current_volume / avg_volume
                confirmations['volume_confirmation'] = volume_ratio >= 1.5
            
            # Price trend confirmation (simplified)
            price = market_data.get('price', 0)
            prev_close = market_data.get('prev_close', price)
            if prev_close > 0:
                price_change = (price - prev_close) / prev_close
                confirmations['trend_confirmation'] = abs(price_change) >= 0.02  # 2% move
            
            # Add more technical confirmations as needed
            
            return confirmations
            
        except Exception as e:
            logger.warning(f"Error getting technical confirmations: {e}")
            return {}
    
    def update_thresholds(self, **kwargs) -> None:
        """Update filtering thresholds"""
        for key, value in kwargs.items():
            if hasattr(self.thresholds, key):
                setattr(self.thresholds, key, value)
                logger.info(f"Updated threshold {key} to {value}")
