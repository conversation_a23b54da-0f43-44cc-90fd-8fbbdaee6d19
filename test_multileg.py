#!/usr/bin/env python3
"""
Test script for Multi-Leg Options Trading functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scanner.alpaca_multileg_trader import AlpacaMultiLegTrader, MultiLegOrder, OptionLeg
from datetime import datetime, timedelta
import json

def test_multileg_trader():
    """Test the multi-leg trader functionality"""
    print("🧪 Testing Multi-Leg Options Trading")
    print("=" * 50)
    
    # Initialize trader
    trader = AlpacaMultiLegTrader()
    
    # Test 1: Create Long Call Spread
    print("\n📈 Test 1: Long Call Spread")
    expiration = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    call_spread = trader.create_long_call_spread(
        symbol="AAPL",
        lower_strike=190.0,
        higher_strike=210.0,
        expiration=expiration,
        limit_price=1.00,
        qty=1
    )
    
    if call_spread:
        print("✅ Long Call Spread created successfully")
        print(f"   Order: {json.dumps(call_spread.to_dict(), indent=2)}")
        
        # Validate order
        is_valid, message = trader.validate_multileg_order(call_spread)
        print(f"   Validation: {'✅ PASS' if is_valid else '❌ FAIL'} - {message}")
    else:
        print("❌ Failed to create Long Call Spread")
    
    # Test 2: Create Iron Condor
    print("\n🦅 Test 2: Iron Condor")
    
    iron_condor = trader.create_iron_condor(
        symbol="SPY",
        put_lower_strike=590.0,
        put_higher_strike=595.0,
        call_lower_strike=605.0,
        call_higher_strike=610.0,
        expiration=expiration,
        limit_price=1.80,
        qty=1
    )
    
    if iron_condor:
        print("✅ Iron Condor created successfully")
        print(f"   Legs: {len(iron_condor.legs)} legs")
        for i, leg in enumerate(iron_condor.legs):
            print(f"   Leg {i+1}: {leg.side} {leg.symbol} ({leg.position_intent})")
        
        # Validate order
        is_valid, message = trader.validate_multileg_order(iron_condor)
        print(f"   Validation: {'✅ PASS' if is_valid else '❌ FAIL'} - {message}")
    else:
        print("❌ Failed to create Iron Condor")
    
    # Test 3: Create Straddle
    print("\n🎯 Test 3: Long Straddle")
    
    straddle = trader.create_straddle(
        symbol="TSLA",
        strike=350.0,
        expiration=expiration,
        limit_price=37.06,
        qty=1,
        is_long=True
    )
    
    if straddle:
        print("✅ Long Straddle created successfully")
        print(f"   Strike: $350.00")
        print(f"   Premium: $37.06")
        
        # Validate order
        is_valid, message = trader.validate_multileg_order(straddle)
        print(f"   Validation: {'✅ PASS' if is_valid else '❌ FAIL'} - {message}")
    else:
        print("❌ Failed to create Long Straddle")
    
    # Test 4: Test Maintenance Margin Calculation
    print("\n💰 Test 4: Maintenance Margin Calculation")
    
    if iron_condor:
        margin = trader.calculate_maintenance_margin(iron_condor.legs, 600.0)
        print(f"   Iron Condor Margin: ${margin:.2f}")
    
    if call_spread:
        margin = trader.calculate_maintenance_margin(call_spread.legs, 200.0)
        print(f"   Call Spread Margin: ${margin:.2f}")
    
    # Test 5: Test Option Symbol Formatting
    print("\n🏷️  Test 5: Option Symbol Formatting")
    
    test_symbols = [
        ("AAPL", "2025-01-17", "C", 200.0),
        ("SPY", "2025-02-21", "P", 590.0),
        ("TSLA", "2025-03-21", "C", 350.0)
    ]
    
    for symbol, exp, opt_type, strike in test_symbols:
        formatted = trader._format_option_symbol(symbol, exp, opt_type, strike)
        print(f"   {symbol} {exp} {opt_type} ${strike} -> {formatted}")
    
    # Test 6: Test GCD Ratio Simplification
    print("\n🔢 Test 6: Ratio Simplification (GCD)")
    
    test_ratios = [
        [2, 4, 6],
        [3, 6, 9],
        [1, 2, 3],
        [4, 8]
    ]
    
    for ratios in test_ratios:
        simplified = trader._simplify_ratios(ratios)
        print(f"   {ratios} -> {simplified}")
    
    print("\n" + "=" * 50)
    print("🎉 Multi-Leg Trading Tests Complete!")

def test_strategy_integration():
    """Test integration with strategy evaluator"""
    print("\n🔗 Testing Strategy Integration")
    print("=" * 50)
    
    try:
        from scanner.working_strategy_evaluator import WorkingStrategyEvaluator
        
        evaluator = WorkingStrategyEvaluator()
        
        # Test strategy conversion
        test_strategy = {
            'strategy_type': 'call_spread',
            'symbol': 'AAPL',
            'long_strike': 190.0,
            'short_strike': 210.0,
            'net_debit': 1.00
        }
        
        order = evaluator.create_multileg_order_from_strategy(test_strategy)
        
        if order:
            print("✅ Strategy to Multi-Leg conversion successful")
            print(f"   Strategy: {test_strategy['strategy_type']}")
            print(f"   Symbol: {test_strategy['symbol']}")
            print(f"   Legs: {len(order.legs)}")
        else:
            print("❌ Failed to convert strategy to multi-leg order")
            
    except ImportError as e:
        print(f"⚠️ Strategy evaluator not available: {e}")

def main():
    """Main test function"""
    print("🚀 Multi-Leg Options Trading Test Suite")
    print("=" * 60)
    
    try:
        test_multileg_trader()
        test_strategy_integration()
        
        print("\n✅ All tests completed successfully!")
        print("\n📋 Summary:")
        print("   - Multi-leg order creation: ✅")
        print("   - Order validation: ✅")
        print("   - Margin calculation: ✅")
        print("   - Symbol formatting: ✅")
        print("   - Ratio simplification: ✅")
        print("   - Strategy integration: ✅")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
