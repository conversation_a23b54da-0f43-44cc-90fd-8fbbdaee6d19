<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Continuous Auto Scanner - AI Options Trader</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
    <style>
        .scanner-status {
            border-left: 4px solid #28a745;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .strategy-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .strategy-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .order-card {
            border-left: 4px solid #ffc107;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .auto-refresh {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> AI Options Trader
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/trading">Paper Trading</a>
                <a class="nav-link active" href="/auto-scanner">Auto Scanner</a>
                <a class="nav-link" href="/multileg">Multi-Leg</a>
                <a class="nav-link" href="/auto-trading">Auto Trading</a>
                <a class="nav-link" href="/hedging">Hedging</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Scanner Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card scanner-status">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-radar"></i> Continuous Multi-Leg Scanner</h5>
                        <div>
                            <span id="scannerStatus" class="status-indicator status-stopped"></span>
                            <span id="scannerStatusText">Stopped</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <p class="mb-2">
                                    <strong>Automated scanning of 200-500 stocks across all sectors</strong><br>
                                    Generates advanced multi-leg strategies (spreads, iron condors, straddles) using real Alpaca options data
                                </p>
                                <div class="d-flex gap-2">
                                    <button id="startScannerBtn" class="btn btn-success">
                                        <i class="fas fa-play"></i> Start Scanner
                                    </button>
                                    <button id="stopScannerBtn" class="btn btn-danger" disabled>
                                        <i class="fas fa-stop"></i> Stop Scanner
                                    </button>
                                    <button id="configBtn" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#configModal">
                                        <i class="fas fa-cog"></i> Configure
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h4 id="currentBatch" class="text-primary mb-0">-</h4>
                                            <small class="text-muted">Current Batch</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h4 id="universeSize" class="text-info mb-0">-</h4>
                                            <small class="text-muted">Total Stocks</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h3 id="scansCompleted" class="mb-0">0</h3>
                        <small>Scans Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h3 id="strategiesGenerated" class="mb-0">0</h3>
                        <small>Strategies Generated</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h3 id="ordersCreated" class="mb-0">0</h3>
                        <small>Orders Created</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <h3 id="activeStrategies" class="mb-0">0</h3>
                        <small>Active Strategies</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Active Strategies -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-chart-bar"></i> Active Strategies</h5>
                        <span class="badge bg-primary" id="strategiesCount">0</span>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="activeStrategiesList">
                            <p class="text-muted text-center">No active strategies</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list-alt"></i> Recent Multi-Leg Orders</h5>
                        <span class="badge bg-warning" id="ordersCount">0</span>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="recentOrdersList">
                            <p class="text-muted text-center">No recent orders</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Batch Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Current Batch Symbols</h5>
                    </div>
                    <div class="card-body">
                        <div id="currentBatchSymbols" class="d-flex flex-wrap gap-2">
                            <span class="text-muted">No batch information available</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Scanner Configuration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <div class="mb-3">
                            <label class="form-label">Minimum Probability of Profit</label>
                            <input type="number" class="form-control" id="minProbability" step="0.01" min="0" max="1" value="0.65">
                            <small class="form-text text-muted">Minimum POP required (0.65 = 65%)</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Minimum Expected Value</label>
                            <input type="number" class="form-control" id="minExpectedValue" step="0.01" min="0" max="1" value="0.15">
                            <small class="form-text text-muted">Minimum EV required (0.15 = 15%)</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Maximum Risk Per Trade ($)</label>
                            <input type="number" class="form-control" id="maxRiskPerTrade" step="100" min="100" value="1000">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Full Scan Interval (seconds)</label>
                            <input type="number" class="form-control" id="scanInterval" step="60" min="60" value="300">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quick Scan Interval (seconds)</label>
                            <input type="number" class="form-control" id="quickScanInterval" step="30" min="30" value="60">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoSubmitOrders">
                                <label class="form-check-label" for="autoSubmitOrders">
                                    Auto-submit orders (LIVE TRADING)
                                </label>
                                <small class="form-text text-muted text-danger">⚠️ Enable only for live trading</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveConfigBtn">Save Configuration</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let statusInterval;
        let isScanning = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            startStatusPolling();
        });

        // Start scanner
        document.getElementById('startScannerBtn').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/continuous-scanner/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Continuous scanner started successfully!', 'success');
                    updateScannerButtons(true);
                } else {
                    showAlert('Failed to start scanner: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error starting scanner: ' + error.message, 'danger');
            }
        });

        // Stop scanner
        document.getElementById('stopScannerBtn').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/continuous-scanner/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Continuous scanner stopped successfully!', 'info');
                    updateScannerButtons(false);
                } else {
                    showAlert('Failed to stop scanner: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error stopping scanner: ' + error.message, 'danger');
            }
        });

        // Save configuration
        document.getElementById('saveConfigBtn').addEventListener('click', async function() {
            try {
                const config = {
                    min_probability: parseFloat(document.getElementById('minProbability').value),
                    min_expected_value: parseFloat(document.getElementById('minExpectedValue').value),
                    max_risk_per_trade: parseInt(document.getElementById('maxRiskPerTrade').value),
                    scan_interval: parseInt(document.getElementById('scanInterval').value),
                    quick_scan_interval: parseInt(document.getElementById('quickScanInterval').value),
                    auto_submit_orders: document.getElementById('autoSubmitOrders').checked
                };

                const response = await fetch('/api/continuous-scanner/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('Configuration updated successfully!', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
                } else {
                    showAlert('Failed to update configuration: ' + result.error, 'danger');
                }
            } catch (error) {
                showAlert('Error updating configuration: ' + error.message, 'danger');
            }
        });

        // Update status
        async function updateStatus() {
            try {
                const response = await fetch('/api/continuous-scanner/status');
                const result = await response.json();

                if (result.success) {
                    const status = result.status;
                    
                    // Update scanner status
                    isScanning = status.is_running;
                    updateScannerButtons(isScanning);
                    
                    // Update statistics
                    document.getElementById('scansCompleted').textContent = status.daily_stats.scans_completed;
                    document.getElementById('strategiesGenerated').textContent = status.daily_stats.strategies_generated;
                    document.getElementById('ordersCreated').textContent = status.daily_stats.orders_created;
                    document.getElementById('activeStrategies').textContent = status.active_strategies_count;
                    
                    // Update batch info
                    document.getElementById('currentBatch').textContent = `${status.current_batch}/${status.total_batches}`;
                    document.getElementById('universeSize').textContent = status.universe_size;
                    
                    // Update current batch symbols
                    updateBatchSymbols(status.current_batch_symbols);
                    
                    // Load configuration
                    if (status.configuration) {
                        loadConfiguration(status.configuration);
                    }
                }

                // Update strategies and orders
                await updateStrategiesAndOrders();

            } catch (error) {
                console.error('Error updating status:', error);
            }
        }

        // Update strategies and orders
        async function updateStrategiesAndOrders() {
            try {
                // Get active strategies
                const strategiesResponse = await fetch('/api/continuous-scanner/strategies');
                const strategiesResult = await strategiesResponse.json();
                
                if (strategiesResult.success) {
                    updateStrategiesList(strategiesResult.strategies);
                    document.getElementById('strategiesCount').textContent = strategiesResult.count;
                }

                // Get recent orders
                const ordersResponse = await fetch('/api/continuous-scanner/orders');
                const ordersResult = await ordersResponse.json();
                
                if (ordersResult.success) {
                    updateOrdersList(ordersResult.orders);
                    document.getElementById('ordersCount').textContent = ordersResult.count;
                }

            } catch (error) {
                console.error('Error updating strategies and orders:', error);
            }
        }

        // Update scanner buttons
        function updateScannerButtons(running) {
            const startBtn = document.getElementById('startScannerBtn');
            const stopBtn = document.getElementById('stopScannerBtn');
            const statusIndicator = document.getElementById('scannerStatus');
            const statusText = document.getElementById('scannerStatusText');

            if (running) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                statusIndicator.className = 'status-indicator status-running auto-refresh';
                statusText.textContent = 'Running';
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
                statusIndicator.className = 'status-indicator status-stopped';
                statusText.textContent = 'Stopped';
            }
        }

        // Update batch symbols
        function updateBatchSymbols(symbols) {
            const container = document.getElementById('currentBatchSymbols');
            
            if (symbols && symbols.length > 0) {
                container.innerHTML = symbols.map(symbol => 
                    `<span class="badge bg-secondary">${symbol}</span>`
                ).join(' ');
            } else {
                container.innerHTML = '<span class="text-muted">No batch information available</span>';
            }
        }

        // Update strategies list
        function updateStrategiesList(strategies) {
            const container = document.getElementById('activeStrategiesList');
            
            if (Object.keys(strategies).length === 0) {
                container.innerHTML = '<p class="text-muted text-center">No active strategies</p>';
                return;
            }

            let html = '';
            for (const [key, data] of Object.entries(strategies)) {
                const strategyData = data.strategies && data.strategies[0];
                if (strategyData) {
                    html += `
                        <div class="strategy-card card mb-2">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${strategyData.symbol} - ${strategyData.strategy_type}</h6>
                                        <small class="text-muted">POP: ${(strategyData.probability_of_profit * 100).toFixed(1)}% | EV: ${(strategyData.expected_value * 100).toFixed(1)}%</small>
                                    </div>
                                    <span class="badge bg-success">${data.count} strategies</span>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
            
            container.innerHTML = html;
        }

        // Update orders list
        function updateOrdersList(orders) {
            const container = document.getElementById('recentOrdersList');
            
            if (orders.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">No recent orders</p>';
                return;
            }

            let html = '';
            orders.slice(0, 10).forEach(order => {
                const strategy = order.strategy;
                const statusBadge = order.status === 'submitted' ? 'bg-success' : 
                                   order.status === 'created' ? 'bg-warning' : 'bg-danger';
                
                html += `
                    <div class="order-card card mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${strategy.symbol} - ${strategy.strategy_type}</h6>
                                    <small class="text-muted">
                                        Risk: $${Math.abs(strategy.max_loss).toFixed(2)} | 
                                        ${new Date(order.created_time).toLocaleTimeString()}
                                    </small>
                                </div>
                                <span class="badge ${statusBadge}">${order.status}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Load configuration
        function loadConfiguration(config) {
            document.getElementById('minProbability').value = config.min_probability;
            document.getElementById('minExpectedValue').value = config.min_expected_value;
            document.getElementById('maxRiskPerTrade').value = config.max_risk_per_trade;
            document.getElementById('scanInterval').value = config.scan_interval;
            document.getElementById('quickScanInterval').value = config.quick_scan_interval;
            document.getElementById('autoSubmitOrders').checked = config.auto_submit_orders || false;
        }

        // Start status polling
        function startStatusPolling() {
            if (statusInterval) clearInterval(statusInterval);
            statusInterval = setInterval(updateStatus, 5000); // Update every 5 seconds
        }

        // Show alert
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
