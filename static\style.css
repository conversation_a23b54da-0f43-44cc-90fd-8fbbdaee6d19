/* AI Options Scanner - Custom Styles */

:root {
    --primary-color: #0066cc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    line-height: 1.6;
}

/* Custom Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
}

/* Table Styles */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

/* Notes column - allow full text display */
.table td:last-child {
    max-width: 200px;
    word-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
}

/* Recommendation row animations */
.new-recommendation {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* High value highlighting */
.high-ev-row {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid var(--success-color);
}

.high-pop-row {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 4px solid var(--primary-color);
}

/* Action buttons */
.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border-right: none;
}

/* Status Cards */
.card.border-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.card.border-success {
    border-left: 4px solid var(--success-color) !important;
}

.card.border-info {
    border-left: 4px solid var(--info-color) !important;
}

.card.border-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #fff !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff !important;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0052a3, var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Table Styles */
.table {
    margin-bottom: 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: var(--dark-color);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 102, 204, 0.05);
    transform: scale(1.01);
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* Progress Bar */
.progress {
    height: 1.2rem;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transition: width 0.6s ease;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.6rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.form-range {
    height: 0.5rem;
}

.form-range::-webkit-slider-thumb {
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    transition: var(--transition);
}

.form-range::-webkit-slider-thumb:hover {
    background: #0052a3;
    transform: scale(1.1);
}

/* Badges and Labels */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.8rem;
    border-radius: var(--border-radius);
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), #bd2130) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800) !important;
    color: #212529 !important;
}

/* Strategy Type Badges */
.strategy-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white !important; /* Ensure white text on all strategy badges */
    text-shadow: 0 1px 2px rgba(0,0,0,0.3); /* Add text shadow for better readability */
}

/* Enhanced Strategy Types */
.strategy-enhanced_naked_call {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white !important;
}
.strategy-enhanced_naked_put {
    background: linear-gradient(135deg, #dc3545, #bd2130);
    color: white !important;
}
.strategy-call_spread {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white !important;
}
.strategy-put_spread {
    background: linear-gradient(135deg, #fd7e14, #e55a00);
    color: white !important;
}
.strategy-iron_condor {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    color: white !important;
}
.strategy-straddle {
    background: linear-gradient(135deg, #e83e8c, #c2185b);
    color: white !important;
}
.strategy-strangle {
    background: linear-gradient(135deg, #20c997, #17a085);
    color: white !important;
}

/* Legacy Strategy Types (for backward compatibility) */
.strategy-naked_call {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white !important;
}
.strategy-naked_put {
    background: linear-gradient(135deg, #dc3545, #bd2130);
    color: white !important;
}
.strategy-long_straddle {
    background: linear-gradient(135deg, #e83e8c, #c2185b);
    color: white !important;
}
.strategy-long_strangle {
    background: linear-gradient(135deg, #20c997, #17a085);
    color: white !important;
}

/* Risk Indicators */
.risk-low { color: var(--success-color); font-weight: 600; }
.risk-medium { color: var(--warning-color); font-weight: 600; }
.risk-high { color: var(--danger-color); font-weight: 600; }

.pop-excellent { color: #28a745; font-weight: 700; }
.pop-good { color: #17a2b8; font-weight: 600; }
.pop-fair { color: #ffc107; font-weight: 600; }
.pop-poor { color: #dc3545; font-weight: 600; }

/* EV Indicators */
.ev-positive { color: var(--success-color); font-weight: 700; }
.ev-negative { color: var(--danger-color); font-weight: 700; }

/* Volume Indicators */
.volume-high { color: var(--success-color); }
.volume-medium { color: var(--warning-color); }
.volume-low { color: var(--danger-color); }

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0052a3);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

/* Loading Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s infinite;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Risk Management Rules */
.risk-rule {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0.5rem;
}

.risk-rule:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.risk-rule strong {
    color: var(--dark-color);
    display: inline-block;
    min-width: 150px;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-idle {
    background: #6c757d20;
    color: #6c757d;
}

.status-scanning {
    background: #007bff20;
    color: #007bff;
    animation: pulse 1.5s infinite;
}

.status-complete {
    background: #28a74520;
    color: #28a745;
}

.status-error {
    background: #dc354520;
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .display-6 {
        font-size: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table td, .table th {
        padding: 0.5rem 0.3rem;
        font-size: 0.8rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.shadow-custom {
    box-shadow: var(--box-shadow);
}

.border-custom {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
}

/* Animation for new recommendations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.new-recommendation {
    animation: slideInUp 0.5s ease-out;
}

/* Highlight for high-value recommendations */
.high-ev-row {
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    border-left: 4px solid var(--success-color);
}

.high-pop-row {
    background: linear-gradient(90deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    border-left: 4px solid var(--info-color);
}

/* Footer */
footer {
    margin-top: auto;
    background: var(--dark-color) !important;
}

footer small {
    line-height: 1.4;
    opacity: 0.8;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0052a3;
}

/* Print Styles */
@media print {
    .navbar, .btn, .modal, footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .card-header {
        background: #f8f9fa !important;
        color: #212529 !important;
    }
}
