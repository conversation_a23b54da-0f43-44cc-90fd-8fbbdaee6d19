import logging
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import TimeFrame
import pandas as pd

logger = logging.getLogger(__name__)

class AlpacaClient:
    """Official Alpaca API client for live trading and market data"""
    
    def __init__(self):
        self.api_key = os.getenv('ALPACA_API_KEY', os.getenv('APCA_API_KEY_ID'))
        self.secret_key = os.getenv('ALPACA_SECRET_KEY', os.getenv('APCA_API_SECRET_KEY'))
        self.base_url = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')
        
        if not self.api_key or not self.secret_key:
            logger.warning("Alpaca API credentials not found. Using paper trading mode.")
            self.api_key = 'PKGMZ07LLYLQNEJ1SXDA'
            self.secret_key = 'xlIfsGgAhi9lL1eeOGKa2HWyVeTKpc2zaImTwb5r'
            self.base_url = 'https://paper-api.alpaca.markets'
        
        try:
            self.api = tradeapi.REST(
                key_id=self.api_key,
                secret_key=self.secret_key,
                base_url=self.base_url,
                api_version='v2'
            )
            
            # Test connection
            account = self.api.get_account()
            logger.info(f"Connected to Alpaca - Account Status: {account.status}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca API: {e}")
            self.api = None
    
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            if not self.api:
                return {'error': 'API not initialized'}
            
            account = self.api.get_account()
            return {
                'status': account.status,
                'buying_power': float(account.buying_power),
                'cash': float(account.cash),
                'portfolio_value': float(account.portfolio_value),
                'equity': float(account.equity),
                'day_trade_count': account.day_trade_count,
                'pattern_day_trader': account.pattern_day_trader,
                'trading_blocked': account.trading_blocked,
                'account_blocked': account.account_blocked
            }
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {'error': str(e)}
    
    def get_market_data(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get real-time market data for symbols"""
        try:
            if not self.api or not symbols:
                return {}
            
            # Get latest quotes
            quotes = self.api.get_latest_quotes(symbols)
            
            # Get latest trades
            trades = self.api.get_latest_trades(symbols)
            
            # Get bars for additional data
            bars = self.api.get_bars(
                symbols, 
                TimeFrame.Day, 
                start=datetime.now() - timedelta(days=2), 
                end=datetime.now(),
                limit=2
            ).df
            
            result = {}
            for symbol in symbols:
                try:
                    quote = quotes.get(symbol)
                    trade = trades.get(symbol)
                    
                    if not quote or not trade:
                        continue
                    
                    # Get bar data for this symbol
                    symbol_bars = bars[bars.index.get_level_values('symbol') == symbol]
                    latest_bar = symbol_bars.iloc[-1] if not symbol_bars.empty else None
                    prev_bar = symbol_bars.iloc[-2] if len(symbol_bars) >= 2 else None
                    
                    current_price = float(trade.price)
                    prev_close = float(prev_bar.close) if prev_bar is not None else current_price
                    
                    result[symbol] = {
                        'symbol': symbol,
                        'price': current_price,
                        'bid': float(quote.bid_price),
                        'ask': float(quote.ask_price),
                        'bid_size': quote.bid_size,
                        'ask_size': quote.ask_size,
                        'volume': int(latest_bar.volume) if latest_bar is not None else 0,
                        'change': current_price - prev_close,
                        'change_percent': ((current_price - prev_close) / prev_close) * 100 if prev_close > 0 else 0,
                        'high': float(latest_bar.high) if latest_bar is not None else current_price,
                        'low': float(latest_bar.low) if latest_bar is not None else current_price,
                        'open': float(latest_bar.open) if latest_bar is not None else current_price,
                        'prev_close': prev_close,
                        'timestamp': trade.timestamp.isoformat(),
                        'data_source': 'alpaca'
                    }
                    
                except Exception as e:
                    logger.error(f"Error processing data for {symbol}: {e}")
                    continue
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {}
    
    def get_historical_bars(self, symbol: str, timeframe: str = '1Day', limit: int = 100) -> pd.DataFrame:
        """Get historical price data"""
        try:
            if not self.api:
                return pd.DataFrame()
            
            # Map timeframe
            tf_map = {
                '1Min': TimeFrame.Minute,
                '5Min': TimeFrame(5, TimeFrame.Minute),
                '15Min': TimeFrame(15, TimeFrame.Minute),
                '1Hour': TimeFrame.Hour,
                '1Day': TimeFrame.Day
            }
            
            timeframe_obj = tf_map.get(timeframe, TimeFrame.Day)
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=limit)
            
            bars = self.api.get_bars(
                symbol,
                timeframe_obj,
                start=start_time,
                end=end_time,
                limit=limit
            ).df
            
            if bars.empty:
                return pd.DataFrame()
            
            # Reset index to get timestamp as column
            bars = bars.reset_index()
            bars['date'] = bars['timestamp']
            
            return bars[['date', 'open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_option_contracts(self, symbol: str) -> List[Dict]:
        """Get available option contracts for a symbol using proper Alpaca options API"""
        try:
            if not self.api:
                logger.warning("Alpaca API not initialized")
                return []

            logger.info(f"🔍 Getting option contracts for {symbol} from Alpaca")

            # Use the correct Alpaca options API endpoint
            # Note: This requires options trading permissions on Alpaca account
            try:
                # Try the new options API format
                contracts = self.api.list_option_contracts(
                    underlying_symbol=symbol,
                    status='active',
                    expiration_date_gte=datetime.now().date(),
                    expiration_date_lte=(datetime.now() + timedelta(days=60)).date(),
                    limit=200  # Limit to prevent too much data
                )

                if not contracts:
                    logger.warning(f"No option contracts found for {symbol}")
                    return []

                result = []
                for contract in contracts:
                    try:
                        result.append({
                            'id': getattr(contract, 'id', ''),
                            'symbol': getattr(contract, 'symbol', ''),
                            'underlying_symbol': getattr(contract, 'underlying_symbol', symbol),
                            'option_type': getattr(contract, 'type', 'call'),
                            'strike': float(getattr(contract, 'strike_price', 0)),
                            'expiry': getattr(contract, 'expiration_date', datetime.now().date()).isoformat(),
                            'dte': (getattr(contract, 'expiration_date', datetime.now().date()) - datetime.now().date()).days,
                            'size': getattr(contract, 'size', 100),
                            'status': getattr(contract, 'status', 'active')
                        })
                    except Exception as contract_error:
                        logger.debug(f"Error processing contract: {contract_error}")
                        continue

                logger.info(f"✅ Found {len(result)} option contracts for {symbol}")
                return result

            except AttributeError as attr_error:
                logger.error(f"Alpaca options API method not available: {attr_error}")
                logger.info("This may require options trading permissions or live account")
                return []

        except Exception as e:
            logger.error(f"Error getting option contracts for {symbol}: {e}")
            if "401" in str(e) or "Unauthorized" in str(e):
                logger.error("❌ Alpaca options API access denied - check account permissions")
            return []
    
    def get_option_quotes(self, contract_symbols: List[str]) -> Dict[str, Dict]:
        """Get option quotes for contract symbols using proper Alpaca options API"""
        try:
            if not self.api or not contract_symbols:
                return {}

            logger.info(f"🔍 Getting option quotes for {len(contract_symbols)} contracts")

            # Get latest option quotes using proper API
            try:
                quotes = self.api.get_latest_option_quotes(contract_symbols)

                if not quotes:
                    logger.warning("No option quotes returned from Alpaca")
                    return {}

                result = {}
                for symbol, quote in quotes.items():
                    if quote:
                        try:
                            bid = float(getattr(quote, 'bid_price', 0)) if getattr(quote, 'bid_price', None) else 0
                            ask = float(getattr(quote, 'ask_price', 0)) if getattr(quote, 'ask_price', None) else 0

                            result[symbol] = {
                                'bid': bid,
                                'ask': ask,
                                'bid_size': getattr(quote, 'bid_size', 0),
                                'ask_size': getattr(quote, 'ask_size', 0),
                                'last_price': (bid + ask) / 2 if bid > 0 and ask > 0 else 0,
                                'mid_price': (bid + ask) / 2 if bid > 0 and ask > 0 else 0,
                                'timestamp': getattr(quote, 'timestamp', datetime.now()).isoformat() if hasattr(getattr(quote, 'timestamp', None), 'isoformat') else datetime.now().isoformat()
                            }
                        except Exception as quote_error:
                            logger.debug(f"Error processing quote for {symbol}: {quote_error}")
                            continue

                logger.info(f"✅ Got quotes for {len(result)} option contracts")
                return result

            except AttributeError as attr_error:
                logger.error(f"Alpaca options quotes API method not available: {attr_error}")
                return {}

        except Exception as e:
            logger.error(f"Error getting option quotes: {e}")
            if "401" in str(e) or "Unauthorized" in str(e):
                logger.error("❌ Alpaca options quotes API access denied - check account permissions")
            return {}
    
    def place_option_order(self, symbol: str, option_symbol: str, qty: int, side: str, 
                          order_type: str = 'market', limit_price: float = None) -> Dict:
        """Place an option order"""
        try:
            if not self.api:
                return {'error': 'API not initialized'}
            
            order_data = {
                'symbol': option_symbol,
                'qty': qty,
                'side': side,  # 'buy' or 'sell'
                'type': order_type,  # 'market' or 'limit'
                'time_in_force': 'day'
            }
            
            if order_type == 'limit' and limit_price:
                order_data['limit_price'] = limit_price
            
            order = self.api.submit_order(**order_data)
            
            return {
                'id': order.id,
                'status': order.status,
                'symbol': order.symbol,
                'qty': int(order.qty),
                'side': order.side,
                'order_type': order.order_type,
                'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                'filled_at': order.filled_at.isoformat() if order.filled_at else None,
                'filled_qty': int(order.filled_qty) if order.filled_qty else 0,
                'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else 0
            }
            
        except Exception as e:
            logger.error(f"Error placing option order: {e}")
            return {'error': str(e)}
    
    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        try:
            if not self.api:
                return []
            
            positions = self.api.list_positions()
            
            result = []
            for pos in positions:
                result.append({
                    'symbol': pos.symbol,
                    'qty': int(pos.qty),
                    'side': 'long' if int(pos.qty) > 0 else 'short',
                    'market_value': float(pos.market_value),
                    'cost_basis': float(pos.cost_basis),
                    'unrealized_pl': float(pos.unrealized_pl),
                    'unrealized_plpc': float(pos.unrealized_plpc),
                    'current_price': float(pos.current_price),
                    'avg_entry_price': float(pos.avg_entry_price)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def get_orders(self, status: str = 'all') -> List[Dict]:
        """Get orders with optional status filter"""
        try:
            if not self.api:
                return []
            
            orders = self.api.list_orders(status=status, limit=100)
            
            result = []
            for order in orders:
                result.append({
                    'id': order.id,
                    'symbol': order.symbol,
                    'qty': int(order.qty),
                    'side': order.side,
                    'order_type': order.order_type,
                    'status': order.status,
                    'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                    'filled_at': order.filled_at.isoformat() if order.filled_at else None,
                    'filled_qty': int(order.filled_qty) if order.filled_qty else 0,
                    'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else 0,
                    'limit_price': float(order.limit_price) if order.limit_price else None,
                    'stop_price': float(order.stop_price) if order.stop_price else None
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    def cancel_order(self, order_id: str) -> Dict:
        """Cancel an order"""
        try:
            if not self.api:
                return {'error': 'API not initialized'}
            
            self.api.cancel_order(order_id)
            return {'status': 'cancelled', 'order_id': order_id}
            
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return {'error': str(e)}
    
    def get_portfolio_history(self, period: str = '1D') -> Dict:
        """Get portfolio performance history"""
        try:
            if not self.api:
                return {}
            
            history = self.api.get_portfolio_history(period=period)
            
            return {
                'timestamp': [t.isoformat() for t in history.timestamp],
                'equity': [float(e) for e in history.equity],
                'profit_loss': [float(pl) for pl in history.profit_loss],
                'profit_loss_pct': [float(plp) for plp in history.profit_loss_pct],
                'base_value': float(history.base_value),
                'timeframe': history.timeframe
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio history: {e}")
            return {}

# Global instance
alpaca_client = AlpacaClient()