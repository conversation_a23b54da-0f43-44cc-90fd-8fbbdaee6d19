#!/usr/bin/env python3
"""
Debug script to test StrategyEvaluator output for a single symbol
"""

import logging
import json
from scanner.data_fetcher import DataFetcher
from scanner.strategy_evaluator import StrategyEvaluator
from scanner.risk_manager import RiskManager

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

# Also set scanner loggers to DEBUG
logging.getLogger('scanner.data_fetcher').setLevel(logging.DEBUG)
logging.getLogger('scanner.strategy_evaluator').setLevel(logging.DEBUG)
logging.getLogger('scanner.probability_filter').setLevel(logging.DEBUG)

def debug_single_symbol(symbol="SPY"):
    """Debug strategy evaluation for a single symbol"""
    
    logger.info(f"🔍 Debugging strategy evaluation for {symbol}")
    
    # Initialize components
    data_fetcher = DataFetcher()
    strategy_evaluator = StrategyEvaluator()
    risk_manager = RiskManager()
    
    # Get market data
    logger.info(f"📊 Getting market data for {symbol}...")
    market_data = data_fetcher.get_market_data_batch([symbol])
    symbol_market_data = market_data.get(symbol)
    
    if not symbol_market_data:
        logger.error(f"❌ No market data for {symbol}")
        return
    
    logger.info(f"✅ Market data: Price=${symbol_market_data.get('price', 0):.2f}, Volume={symbol_market_data.get('volume', 0):,}")
    
    # Get options data
    logger.info(f"📊 Getting options data for {symbol}...")
    options_data = data_fetcher.get_option_chain(symbol)
    
    if not options_data:
        logger.error(f"❌ No options data for {symbol}")
        return
    
    logger.info(f"✅ Options data: {len(options_data.get('calls', []))} calls, {len(options_data.get('puts', []))} puts")
    
    # Evaluate strategies
    logger.info(f"🔍 Evaluating strategies for {symbol}...")
    strategies = strategy_evaluator.evaluate_all_strategies(symbol, symbol_market_data, options_data)
    
    logger.info(f"✅ StrategyEvaluator generated {len(strategies)} strategies")
    
    # Debug first few strategies
    for i, strategy in enumerate(strategies[:5]):
        logger.info(f"  Strategy {i+1}:")
        logger.info(f"    Type: {strategy.get('strategy', 'unknown')}")
        logger.info(f"    Expected Value: ${strategy.get('ev', 0):.2f}")
        logger.info(f"    Probability of Profit: {strategy.get('pop', 0):.1%}")
        logger.info(f"    Max Loss: ${strategy.get('max_loss', 0):.2f}")
        logger.info(f"    Risk-Reward: {strategy.get('risk_reward', 0):.2%}")
        logger.info(f"    Volume: {strategy.get('volume', 0)}")
        logger.info(f"    All fields: {list(strategy.keys())}")
        logger.info("")
    
    # Test RiskManager filtering
    logger.info(f"🔍 Testing RiskManager filtering...")
    filtered_strategies = risk_manager.filter_strategies(strategies)
    
    logger.info(f"✅ RiskManager filtered to {len(filtered_strategies)} strategies")
    
    if filtered_strategies:
        logger.info("✅ STRATEGIES PASSED RISK MANAGEMENT!")
        for i, strategy in enumerate(filtered_strategies[:3]):
            logger.info(f"  Filtered Strategy {i+1}: {strategy.get('strategy', 'unknown')} - EV: ${strategy.get('ev', 0):.2f}")
    else:
        logger.warning("❌ NO STRATEGIES PASSED RISK MANAGEMENT FILTERS")
        
        # Debug why strategies were rejected
        logger.info("🔍 Debugging rejection reasons...")
        for i, strategy in enumerate(strategies[:3]):
            logger.info(f"  Strategy {i+1} ({strategy.get('strategy', 'unknown')}):")
            
            # Check each filter condition
            max_loss = strategy.get('max_loss', 0)
            max_allowed_loss = 100000 * 0.10  # 10% of $100k
            logger.info(f"    Max Loss: ${max_loss:.2f} vs Limit: ${max_allowed_loss:.2f} - {'PASS' if max_loss <= max_allowed_loss else 'FAIL'}")
            
            ev = strategy.get('ev', 0)
            logger.info(f"    Expected Value: ${ev:.2f} - {'PASS' if ev > 0 else 'FAIL'}")
            
            pop = strategy.get('pop', 0)
            logger.info(f"    POP: {pop:.1%} vs Min: 20% - {'PASS' if pop >= 0.2 else 'FAIL'}")
            
            risk_reward = strategy.get('risk_reward', 0)
            logger.info(f"    Risk-Reward: {risk_reward:.2%} vs Min: 10% - {'PASS' if risk_reward >= 0.1 else 'FAIL'}")
            
            volume = strategy.get('volume', 0)
            logger.info(f"    Volume: {volume} vs Min: 10 - {'PASS' if volume >= 10 else 'FAIL'}")
            
            logger.info("")

if __name__ == "__main__":
    debug_single_symbol("SPY")
