#!/usr/bin/env python3
"""
Direct test of advanced strategy generation - bypassing all filters
"""

import logging
from scanner.data_fetcher import DataFetcher
from scanner.strategy_evaluator import StrategyEvaluator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_advanced_strategies_direct():
    """Test advanced strategy generation directly"""
    
    logger.info("🔍 Testing advanced strategy generation for SPY")
    
    # Initialize components
    data_fetcher = DataFetcher()
    strategy_evaluator = StrategyEvaluator()
    
    # Get market data
    logger.info("📊 Getting market data for SPY...")
    market_data = data_fetcher.get_market_data_batch(["SPY"])
    spy_market_data = market_data.get("SPY")
    
    if not spy_market_data:
        logger.error("❌ No market data for SPY")
        return
    
    logger.info(f"✅ Market data: Price=${spy_market_data.get('price', 0):.2f}")
    
    # Get options data
    logger.info("📊 Getting options data for SPY...")
    options_data = data_fetcher.get_option_chain("SPY")
    
    if not options_data:
        logger.error("❌ No options data for SPY")
        return
    
    calls = options_data.get('calls', [])
    puts = options_data.get('puts', [])
    logger.info(f"✅ Options data: {len(calls)} calls, {len(puts)} puts")
    
    # Test individual strategy methods directly
    logger.info("🔍 Testing individual strategy methods...")
    
    # Test naked calls
    logger.info("Testing _evaluate_enhanced_naked_calls...")
    try:
        call_strategies = strategy_evaluator._evaluate_enhanced_naked_calls(
            "SPY", spy_market_data, calls, {}
        )
        logger.info(f"✅ Enhanced naked calls: {len(call_strategies)} strategies")
        if call_strategies:
            logger.info(f"  Example: {call_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced naked calls failed: {e}")
    
    # Test naked puts
    logger.info("Testing _evaluate_enhanced_naked_puts...")
    try:
        put_strategies = strategy_evaluator._evaluate_enhanced_naked_puts(
            "SPY", spy_market_data, puts, {}
        )
        logger.info(f"✅ Enhanced naked puts: {len(put_strategies)} strategies")
        if put_strategies:
            logger.info(f"  Example: {put_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced naked puts failed: {e}")
    
    # Test call spreads
    logger.info("Testing _evaluate_enhanced_call_spreads...")
    try:
        call_spread_strategies = strategy_evaluator._evaluate_enhanced_call_spreads(
            "SPY", spy_market_data, calls, {}
        )
        logger.info(f"✅ Enhanced call spreads: {len(call_spread_strategies)} strategies")
        if call_spread_strategies:
            logger.info(f"  Example: {call_spread_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced call spreads failed: {e}")
    
    # Test put spreads
    logger.info("Testing _evaluate_enhanced_put_spreads...")
    try:
        put_spread_strategies = strategy_evaluator._evaluate_enhanced_put_spreads(
            "SPY", spy_market_data, puts, {}
        )
        logger.info(f"✅ Enhanced put spreads: {len(put_spread_strategies)} strategies")
        if put_spread_strategies:
            logger.info(f"  Example: {put_spread_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced put spreads failed: {e}")
    
    # Test straddles
    logger.info("Testing _evaluate_enhanced_straddles...")
    try:
        straddle_strategies = strategy_evaluator._evaluate_enhanced_straddles(
            "SPY", spy_market_data, calls, puts, {}
        )
        logger.info(f"✅ Enhanced straddles: {len(straddle_strategies)} strategies")
        if straddle_strategies:
            logger.info(f"  Example: {straddle_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced straddles failed: {e}")
    
    # Test iron condors
    logger.info("Testing _evaluate_enhanced_iron_condors...")
    try:
        condor_strategies = strategy_evaluator._evaluate_enhanced_iron_condors(
            "SPY", spy_market_data, calls, puts, {}
        )
        logger.info(f"✅ Enhanced iron condors: {len(condor_strategies)} strategies")
        if condor_strategies:
            logger.info(f"  Example: {condor_strategies[0].get('strategy', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ Enhanced iron condors failed: {e}")
    
    # Test the full evaluate_all_strategies method
    logger.info("🔍 Testing full evaluate_all_strategies method...")
    try:
        all_strategies = strategy_evaluator.evaluate_all_strategies(
            "SPY", spy_market_data, options_data
        )
        logger.info(f"✅ Full evaluation: {len(all_strategies)} strategies")
        
        if all_strategies:
            logger.info("📊 Strategy breakdown:")
            strategy_types = {}
            for strategy in all_strategies:
                strategy_type = strategy.get('strategy', 'unknown')
                strategy_types[strategy_type] = strategy_types.get(strategy_type, 0) + 1
            
            for strategy_type, count in strategy_types.items():
                logger.info(f"  {strategy_type}: {count}")
        else:
            logger.warning("❌ No strategies generated by full evaluation!")
            
    except Exception as e:
        logger.error(f"❌ Full evaluation failed: {e}")

if __name__ == "__main__":
    test_advanced_strategies_direct()
