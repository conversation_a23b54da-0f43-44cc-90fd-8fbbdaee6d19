"""
Continuous Multi-Leg Options Scanner
Automatically scans 200-500 stocks and generates advanced multi-leg strategies
"""

import logging
import threading
import time
import random
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .working_strategy_evaluator import WorkingStrategyEvaluator
from .alpaca_multileg_trader import AlpacaMultiLegTrader
from .data_fetcher import DataFetcher
from .probability_filter import ProbabilityFilter
from .risk_manager import RiskManager

logger = logging.getLogger(__name__)

@dataclass
class ScanResult:
    """Results from a continuous scan cycle"""
    timestamp: datetime
    symbols_scanned: int
    strategies_found: int
    multileg_orders_created: int
    top_strategies: List[Dict]
    scan_duration: float

class ContinuousMultiLegScanner:
    """
    Continuous scanner that automatically scans stocks and generates multi-leg strategies
    """
    
    def __init__(self):
        """Initialize the continuous scanner"""
        self.is_running = False
        self.scanner_thread = None
        self.scan_interval = 300  # 5 minutes between full scans
        self.quick_scan_interval = 60  # 1 minute between quick scans
        
        # Components
        self.data_fetcher = DataFetcher()
        self.strategy_evaluator = WorkingStrategyEvaluator()
        self.multileg_trader = AlpacaMultiLegTrader()
        self.probability_filter = ProbabilityFilter()
        self.risk_manager = RiskManager()
        
        # Stock universe (200-500 stocks across sectors)
        self.stock_universe = self._build_stock_universe()
        self.current_scan_batch = 0
        self.batch_size = 50  # Scan 50 stocks at a time
        
        # Results tracking
        self.scan_results: List[ScanResult] = []
        self.active_strategies: Dict[str, Dict] = {}
        self.daily_stats = {
            'scans_completed': 0,
            'strategies_generated': 0,
            'orders_created': 0,
            'start_time': datetime.now()
        }
        
        # Configuration
        self.auto_submit_orders = False  # Set to True for live trading
        self.min_probability = 0.65  # Minimum probability of profit
        self.min_expected_value = 0.15  # Minimum expected value (15%)
        self.max_risk_per_trade = 1000  # Maximum risk per trade
        
        logger.info("✅ Continuous Multi-Leg Scanner initialized")
    
    def _build_stock_universe(self) -> List[str]:
        """Build a diverse universe of 200-500 stocks across sectors"""
        # Major indices and ETFs
        etfs = ['SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VEA', 'VWO', 'EFA', 'EEM']
        
        # Technology
        tech = ['AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX', 'ADBE',
                'CRM', 'ORCL', 'INTC', 'AMD', 'PYPL', 'UBER', 'LYFT', 'SNAP', 'TWTR', 'ZOOM']
        
        # Healthcare & Biotech
        healthcare = ['JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR', 'BMY', 'AMGN',
                     'GILD', 'BIIB', 'REGN', 'VRTX', 'ILMN', 'MRNA', 'BNTX', 'ZTS', 'CVS', 'ANTM']
        
        # Financial
        financial = ['JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SCHW', 'USB',
                    'PNC', 'TFC', 'COF', 'BK', 'STT', 'V', 'MA', 'PYPL', 'SQ', 'AFRM']
        
        # Energy
        energy = ['XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'PSX', 'VLO', 'KMI', 'OKE',
                 'WMB', 'EPD', 'ET', 'MPLX', 'BKR', 'HAL', 'DVN', 'FANG', 'MRO', 'APA']
        
        # Consumer Discretionary
        consumer_disc = ['AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'SBUX', 'LOW', 'TJX', 'BKNG', 'CMG',
                        'ORLY', 'AZO', 'ULTA', 'RCL', 'CCL', 'MAR', 'HLT', 'MGM', 'WYNN', 'LVS']
        
        # Consumer Staples
        consumer_staples = ['PG', 'KO', 'PEP', 'WMT', 'COST', 'CL', 'KMB', 'GIS', 'K', 'HSY',
                           'MDLZ', 'CPB', 'CAG', 'SJM', 'HRL', 'TSN', 'TAP', 'STZ', 'BF.B', 'PM']
        
        # Industrial
        industrial = ['BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'FDX', 'LMT', 'RTX', 'NOC',
                     'GD', 'DE', 'EMR', 'ETN', 'PH', 'CMI', 'ITW', 'ROK', 'DOV', 'XYL']
        
        # Materials
        materials = ['LIN', 'APD', 'ECL', 'SHW', 'FCX', 'NEM', 'GOLD', 'AA', 'X', 'CLF',
                    'NUE', 'STLD', 'RS', 'VMC', 'MLM', 'PKG', 'IP', 'WRK', 'SON', 'AVY']
        
        # Utilities
        utilities = ['NEE', 'DUK', 'SO', 'D', 'AEP', 'EXC', 'XEL', 'SRE', 'PEG', 'ED',
                    'EIX', 'WEC', 'AWK', 'ATO', 'CMS', 'DTE', 'NI', 'LNT', 'EVRG', 'CNP']
        
        # Real Estate
        real_estate = ['AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
                      'AVB', 'EQR', 'UDR', 'ESS', 'MAA', 'CPT', 'ARE', 'VTR', 'PEAK', 'AMH']
        
        # Communication Services
        communication = ['GOOGL', 'META', 'NFLX', 'DIS', 'CMCSA', 'VZ', 'T', 'CHTR', 'TMUS', 'ATVI',
                        'EA', 'TTWO', 'MTCH', 'PINS', 'TWTR', 'SNAP', 'ROKU', 'SPOT', 'ZM', 'DOCU']
        
        # Combine all sectors
        all_stocks = (etfs + tech + healthcare + financial + energy + consumer_disc + 
                     consumer_staples + industrial + materials + utilities + real_estate + communication)
        
        # Remove duplicates and return
        unique_stocks = list(set(all_stocks))
        logger.info(f"📊 Built stock universe with {len(unique_stocks)} symbols across all sectors")
        return unique_stocks
    
    def start_continuous_scanning(self) -> bool:
        """Start the continuous scanning process"""
        if self.is_running:
            logger.warning("⚠️ Continuous scanner already running")
            return False
        
        try:
            self.is_running = True
            self.daily_stats['start_time'] = datetime.now()
            
            # Start scanner thread
            self.scanner_thread = threading.Thread(target=self._continuous_scan_loop, daemon=True)
            self.scanner_thread.start()
            
            logger.info("🚀 Continuous Multi-Leg Scanner started successfully")
            logger.info(f"📊 Scanning {len(self.stock_universe)} stocks in batches of {self.batch_size}")
            logger.info(f"⏱️ Full scan every {self.scan_interval}s, quick scan every {self.quick_scan_interval}s")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start continuous scanner: {e}")
            self.is_running = False
            return False
    
    def stop_continuous_scanning(self):
        """Stop the continuous scanning process"""
        self.is_running = False
        if self.scanner_thread:
            self.scanner_thread.join(timeout=10)
        logger.info("🛑 Continuous Multi-Leg Scanner stopped")
    
    def _continuous_scan_loop(self):
        """Main continuous scanning loop"""
        logger.info("🔄 Starting continuous scan loop")
        
        last_full_scan = 0
        last_quick_scan = 0
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Determine scan type
                should_full_scan = (current_time - last_full_scan) >= self.scan_interval
                should_quick_scan = (current_time - last_quick_scan) >= self.quick_scan_interval
                
                if should_full_scan:
                    # Full scan of current batch
                    self._run_full_batch_scan()
                    last_full_scan = current_time
                    
                    # Move to next batch
                    self._advance_to_next_batch()
                    
                elif should_quick_scan:
                    # Quick scan of high-priority symbols
                    self._run_quick_scan()
                    last_quick_scan = current_time
                
                # Brief sleep to prevent excessive CPU usage
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"❌ Error in continuous scan loop: {e}")
                time.sleep(30)  # Wait 30 seconds on error
        
        logger.info("🔄 Continuous scan loop ended")
    
    def _run_full_batch_scan(self):
        """Run a full scan on the current batch of stocks"""
        start_time = time.time()
        
        # Get current batch of symbols
        batch_symbols = self._get_current_batch()
        
        logger.info(f"🔍 Starting full batch scan: {len(batch_symbols)} symbols (batch {self.current_scan_batch + 1})")
        
        all_strategies = []
        multileg_orders_created = 0
        
        for symbol in batch_symbols:
            if not self.is_running:
                break
                
            try:
                # Get strategies for this symbol
                strategies = self._scan_symbol_for_strategies(symbol)
                
                if strategies:
                    # Filter for high-quality strategies
                    filtered_strategies = self._filter_strategies(strategies)
                    
                    if filtered_strategies:
                        all_strategies.extend(filtered_strategies)
                        
                        # Create multi-leg orders for top strategies
                        for strategy in filtered_strategies[:2]:  # Top 2 per symbol
                            if self._should_create_multileg_order(strategy):
                                order = self.strategy_evaluator.create_multileg_order_from_strategy(strategy)
                                if order:
                                    multileg_orders_created += 1
                                    self._handle_multileg_order(strategy, order)
                
            except Exception as e:
                logger.debug(f"Error scanning {symbol}: {e}")
                continue
        
        # Record scan results
        scan_duration = time.time() - start_time
        scan_result = ScanResult(
            timestamp=datetime.now(),
            symbols_scanned=len(batch_symbols),
            strategies_found=len(all_strategies),
            multileg_orders_created=multileg_orders_created,
            top_strategies=sorted(all_strategies, key=lambda x: x.get('expected_value', 0), reverse=True)[:10],
            scan_duration=scan_duration
        )
        
        self.scan_results.append(scan_result)
        self.daily_stats['scans_completed'] += 1
        self.daily_stats['strategies_generated'] += len(all_strategies)
        self.daily_stats['orders_created'] += multileg_orders_created
        
        logger.info(f"✅ Batch scan complete: {len(all_strategies)} strategies, {multileg_orders_created} orders created ({scan_duration:.1f}s)")
    
    def _get_current_batch(self) -> List[str]:
        """Get the current batch of symbols to scan"""
        start_idx = self.current_scan_batch * self.batch_size
        end_idx = min(start_idx + self.batch_size, len(self.stock_universe))
        return self.stock_universe[start_idx:end_idx]
    
    def _advance_to_next_batch(self):
        """Move to the next batch of symbols"""
        self.current_scan_batch = (self.current_scan_batch + 1) % ((len(self.stock_universe) + self.batch_size - 1) // self.batch_size)
        if self.current_scan_batch == 0:
            logger.info("🔄 Completed full universe scan, starting over")
    
    def _scan_symbol_for_strategies(self, symbol: str) -> List[Dict]:
        """Scan a single symbol for multi-leg strategies"""
        try:
            # Get market data
            market_data = self.data_fetcher.get_market_data_batch([symbol])
            symbol_data = market_data.get(symbol)
            
            if not symbol_data:
                return []
            
            # Get option chain
            option_chain = self.data_fetcher.get_option_chain(symbol)
            if not option_chain:
                return []
            
            # Generate strategies
            strategies = self.strategy_evaluator.evaluate_all_strategies(symbol, symbol_data, option_chain)
            
            # Add metadata
            for strategy in strategies:
                strategy['symbol'] = symbol
                strategy['scan_time'] = datetime.now().isoformat()
                strategy['market_price'] = symbol_data.get('price', 0)
            
            return strategies
            
        except Exception as e:
            logger.debug(f"Error scanning {symbol}: {e}")
            return []

    def _run_quick_scan(self):
        """Run a quick scan of high-priority symbols"""
        # Quick scan focuses on high-volume, liquid symbols
        priority_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'TSLA', 'AMZN', 'GOOGL', 'META', 'NVDA', 'NFLX']

        logger.info(f"⚡ Running quick scan: {len(priority_symbols)} priority symbols")

        strategies_found = 0
        for symbol in priority_symbols:
            if not self.is_running:
                break

            strategies = self._scan_symbol_for_strategies(symbol)
            if strategies:
                filtered = self._filter_strategies(strategies)
                strategies_found += len(filtered)

                # Update active strategies
                self.active_strategies[symbol] = {
                    'strategies': filtered,
                    'last_update': datetime.now(),
                    'count': len(filtered)
                }

        logger.info(f"⚡ Quick scan complete: {strategies_found} strategies found")

    def _filter_strategies(self, strategies: List[Dict]) -> List[Dict]:
        """Filter strategies based on quality criteria"""
        filtered = []

        for strategy in strategies:
            try:
                # Check probability of profit
                pop = strategy.get('probability_of_profit', 0)
                if pop < self.min_probability:
                    continue

                # Check expected value
                ev = strategy.get('expected_value', 0)
                if ev < self.min_expected_value:
                    continue

                # Check risk
                max_loss = abs(strategy.get('max_loss', 0))
                if max_loss > self.max_risk_per_trade:
                    continue

                # Check for valid multi-leg strategy types
                strategy_type = strategy.get('strategy_type', '')
                if strategy_type in ['call_spread', 'put_spread', 'iron_condor', 'straddle', 'iron_butterfly']:
                    strategy['is_multileg'] = True
                    filtered.append(strategy)

            except Exception as e:
                logger.debug(f"Error filtering strategy: {e}")
                continue

        return filtered

    def _should_create_multileg_order(self, strategy: Dict) -> bool:
        """Determine if a multi-leg order should be created for this strategy"""
        try:
            # Check if it's a multi-leg strategy
            if not strategy.get('is_multileg', False):
                return False

            # Check quality thresholds
            pop = strategy.get('probability_of_profit', 0)
            ev = strategy.get('expected_value', 0)

            # Higher thresholds for order creation
            if pop < 0.70 or ev < 0.20:
                return False

            # Check if we already have an active order for this symbol/strategy
            symbol = strategy.get('symbol', '')
            strategy_type = strategy.get('strategy_type', '')

            active_key = f"{symbol}_{strategy_type}"
            if active_key in self.active_strategies:
                last_order = self.active_strategies[active_key].get('last_order_time')
                if last_order and (datetime.now() - last_order).total_seconds() < 3600:  # 1 hour cooldown
                    return False

            return True

        except Exception as e:
            logger.debug(f"Error checking order creation criteria: {e}")
            return False

    def _handle_multileg_order(self, strategy: Dict, order):
        """Handle a created multi-leg order"""
        try:
            symbol = strategy.get('symbol', '')
            strategy_type = strategy.get('strategy_type', '')

            logger.info(f"📋 Multi-leg order created: {symbol} {strategy_type}")
            logger.info(f"   POP: {strategy.get('probability_of_profit', 0):.1%}")
            logger.info(f"   EV: {strategy.get('expected_value', 0):.1%}")
            logger.info(f"   Max Loss: ${abs(strategy.get('max_loss', 0)):.2f}")

            # Validate order
            is_valid, message = self.multileg_trader.validate_multileg_order(order)

            if is_valid:
                # Store order information
                order_info = {
                    'strategy': strategy,
                    'order': order.to_dict(),
                    'created_time': datetime.now(),
                    'status': 'created',
                    'validation': 'passed'
                }

                # Submit order if auto-submit is enabled
                if self.auto_submit_orders:
                    response = self.multileg_trader.submit_multileg_order(order)
                    if response and 'id' in response:
                        order_info['order_id'] = response['id']
                        order_info['status'] = 'submitted'
                        logger.info(f"✅ Order submitted: {response['id']}")
                    else:
                        order_info['status'] = 'submission_failed'
                        logger.warning(f"⚠️ Order submission failed")

                # Update active strategies
                active_key = f"{symbol}_{strategy_type}"
                self.active_strategies[active_key] = {
                    'order_info': order_info,
                    'last_order_time': datetime.now()
                }

            else:
                logger.warning(f"⚠️ Order validation failed: {message}")

        except Exception as e:
            logger.error(f"❌ Error handling multi-leg order: {e}")

    def get_scan_status(self) -> Dict:
        """Get current scanning status and statistics"""
        current_batch_symbols = self._get_current_batch()

        return {
            'is_running': self.is_running,
            'current_batch': self.current_scan_batch + 1,
            'total_batches': (len(self.stock_universe) + self.batch_size - 1) // self.batch_size,
            'current_batch_symbols': current_batch_symbols,
            'universe_size': len(self.stock_universe),
            'batch_size': self.batch_size,
            'daily_stats': self.daily_stats,
            'active_strategies_count': len(self.active_strategies),
            'recent_scan_results': self.scan_results[-5:] if self.scan_results else [],
            'auto_submit_enabled': self.auto_submit_orders,
            'configuration': {
                'min_probability': self.min_probability,
                'min_expected_value': self.min_expected_value,
                'max_risk_per_trade': self.max_risk_per_trade,
                'scan_interval': self.scan_interval,
                'quick_scan_interval': self.quick_scan_interval
            }
        }

    def get_active_strategies(self) -> Dict:
        """Get currently active strategies"""
        return self.active_strategies

    def get_recent_orders(self) -> List[Dict]:
        """Get recently created multi-leg orders"""
        orders = []
        for _, data in self.active_strategies.items():
            if 'order_info' in data:
                orders.append(data['order_info'])

        # Sort by creation time
        orders.sort(key=lambda x: x.get('created_time', datetime.min), reverse=True)
        return orders[:20]  # Return last 20 orders

    def update_configuration(self, config: Dict):
        """Update scanner configuration"""
        if 'min_probability' in config:
            self.min_probability = config['min_probability']
        if 'min_expected_value' in config:
            self.min_expected_value = config['min_expected_value']
        if 'max_risk_per_trade' in config:
            self.max_risk_per_trade = config['max_risk_per_trade']
        if 'scan_interval' in config:
            self.scan_interval = config['scan_interval']
        if 'quick_scan_interval' in config:
            self.quick_scan_interval = config['quick_scan_interval']
        if 'auto_submit_orders' in config:
            self.auto_submit_orders = config['auto_submit_orders']

        logger.info("⚙️ Scanner configuration updated")

# Global instance
continuous_scanner = ContinuousMultiLegScanner()
